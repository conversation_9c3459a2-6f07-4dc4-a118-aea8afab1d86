@echo off
setlocal enabledelayedexpansion

echo ================================================================================
echo                    OPTIMIZED BTCUSD TRADING BOT LAUNCHER
echo ================================================================================
echo.
echo [INFO] Advanced Trading Bot with Multi-Terminal Support
echo [INFO] Supports 5 MT5 terminals with different model types:
echo [INFO]   Terminal 1: ARIMA (Conservative, M5)
echo [INFO]   Terminal 2: LSTM (Moderate, M15) 
echo [INFO]   Terminal 3: TFT (Moderate-Aggressive, M30)
echo [INFO]   Terminal 4: LSTM+ARIMA Ensemble (Aggressive, H1)
echo [INFO]   Terminal 5: TFT+ARIMA Ensemble (Dynamic, Multi-timeframe)
echo.

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo [INFO] Script directory: %SCRIPT_DIR%
echo [INFO] Current directory: %CD%
echo.

REM Check system requirements
echo [STEP 1/7] Checking system requirements...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo [ERROR] Please install Python 3.8+ and add it to your PATH
    echo [ERROR] Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [SUCCESS] Python version:
python --version
echo.

REM Check Python version (require 3.8+)
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
for /f "tokens=1,2 delims=." %%a in ("%PYTHON_VERSION%") do (
    set MAJOR=%%a
    set MINOR=%%b
)
if %MAJOR% LSS 3 (
    echo [ERROR] Python version %PYTHON_VERSION% is too old. Requires Python 3.8+
    pause
    exit /b 1
)
if %MAJOR% EQU 3 if %MINOR% LSS 8 (
    echo [ERROR] Python version %PYTHON_VERSION% is too old. Requires Python 3.8+
    pause
    exit /b 1
)

echo [SUCCESS] Python version %PYTHON_VERSION% is compatible
echo.

REM Check if required files exist
echo [STEP 2/7] Checking required files...
echo.

set "MISSING_FILES="

if not exist "run_trading_bot.py" (
    echo [ERROR] run_trading_bot.py not found
    set "MISSING_FILES=!MISSING_FILES! run_trading_bot.py"
)

if not exist "trading_bot.py" (
    echo [ERROR] trading_bot.py not found
    set "MISSING_FILES=!MISSING_FILES! trading_bot.py"
)

if not exist "config\credentials.py" (
    echo [ERROR] config\credentials.py not found
    set "MISSING_FILES=!MISSING_FILES! config\credentials.py"
)

if not exist "utils\algo_trading_manager.py" (
    echo [ERROR] utils\algo_trading_manager.py not found
    set "MISSING_FILES=!MISSING_FILES! utils\algo_trading_manager.py"
)

if not exist "requirements.txt" (
    echo [WARNING] requirements.txt not found - cannot verify dependencies
)

if defined MISSING_FILES (
    echo [ERROR] Missing required files: %MISSING_FILES%
    echo [ERROR] Please ensure you are running this script from the correct directory
    echo [ERROR] and that all required files are present
    pause
    exit /b 1
)

echo [SUCCESS] All required files found
echo.

REM Check Python dependencies
echo [STEP 3/7] Checking Python dependencies...
echo.

python -c "import MetaTrader5" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] MetaTrader5 package not installed
    echo [INFO] Installing MetaTrader5...
    pip install MetaTrader5
    if errorlevel 1 (
        echo [ERROR] Failed to install MetaTrader5
        pause
        exit /b 1
    )
)

python -c "import pandas, numpy, torch, sklearn" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Some required packages may be missing
    echo [INFO] Attempting to install requirements...
    if exist "requirements.txt" (
        pip install -r requirements.txt
        if errorlevel 1 (
            echo [WARNING] Some packages failed to install - continuing anyway
        )
    )
)

echo [SUCCESS] Python dependencies checked
echo.

REM Check MT5 terminals
echo [STEP 4/7] Checking MT5 terminal configurations...
echo.

python utils\check_terminals.py

if errorlevel 1 (
    echo [ERROR] Failed to check MT5 terminal configurations
    pause
    exit /b 1
)
echo.

REM Start MT5 Algo Trading maintenance
echo [STEP 5/7] Starting MT5 Algo Trading maintenance...
echo.

echo [INFO] Starting MT5 Algo Trading maintenance process...
echo [INFO] This will keep Algo Trading enabled across all terminals
start "MT5 Algo Trading Maintenance" cmd /k "python utils\run_maintenance.py"

REM Wait for maintenance process to initialize
echo [INFO] Waiting 20 seconds for MT5 terminals to initialize...
timeout /t 20 /nobreak

echo.

REM Verify MT5 Algo Trading status
echo [STEP 6/7] Verifying MT5 Algo Trading status...
echo.

python utils\verify_algo_trading.py

if errorlevel 1 (
    echo [WARNING] Algo Trading verification had issues - check the maintenance window
    echo [WARNING] You may need to manually enable Algo Trading in each MT5 terminal
    echo [WARNING] Look for the 'Algo Trading' button in the MT5 toolbar and ensure it's enabled
)
echo.

REM Start the trading bot
echo [STEP 7/7] Starting Trading Bot...
echo.

echo [INFO] Launching BTCUSD Trading Bot...
echo [INFO] Bot will run in multi-terminal mode by default
echo [INFO] Press Ctrl+C to stop the trading bot
echo [INFO] Monitor the MT5 Algo Trading Maintenance window for status
echo.

echo ================================================================================
echo                              TRADING BOT ACTIVE
echo ================================================================================
echo.

REM Start the trading bot with error handling
python run_trading_bot.py
set "BOT_EXIT_CODE=%ERRORLEVEL%"

echo.
echo ================================================================================
echo                            TRADING BOT STOPPED
echo ================================================================================
echo.

if %BOT_EXIT_CODE% EQU 0 (
    echo [INFO] Trading bot stopped normally
) else (
    echo [WARNING] Trading bot stopped with error code: %BOT_EXIT_CODE%
    echo [WARNING] Check the logs for details
)

echo.
echo [IMPORTANT] DO NOT CLOSE THE "MT5 Algo Trading Maintenance" WINDOW!
echo [IMPORTANT] Keep it running to maintain Algo Trading status
echo [IMPORTANT] Only close it when you're done trading for the day
echo.
echo [INFO] Log files are available in the 'logs' directory
echo [INFO] You can restart the bot by running this script again
echo.

pause
