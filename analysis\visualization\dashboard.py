"""
Visualization Dashboard Module.
This module provides a comprehensive dashboard for visualizing trading bot data.
"""
import logging
import os
import traceback
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from datetime import datetime, timedelta
import json
import torch
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import plotly.io as pio
from jinja2 import Template

# Import visualization modules
from analysis.visualization.time_series_visualizer import TimeSeriesVisualizer
from analysis.visualization.feature_visualizer import FeatureVisualizer
from analysis.visualization.performance_visualizer import PerformanceVisualizer
from analysis.visualization.model_decay_monitor import ModelDecayMonitor
from analysis.visualization.m5_model_visualizer import M5ModelVisualizer

# Configure logger
logger = logging.getLogger('analysis.visualization.dashboard')

class VisualizationDashboard:
    """
    Visualization Dashboard class for creating a comprehensive dashboard.
    """
    def __init__(self,
               output_dir: str = "monitoring/dashboard",
               data_dir: str = "data/storage"):
        """
        Initialize VisualizationDashboard.

        Args:
            output_dir: Directory to save dashboard
            data_dir: Directory with data files
        """
        self.output_dir = output_dir
        self.data_dir = data_dir

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Initialize visualization components
        self.time_series_viz = TimeSeriesVisualizer(output_dir=os.path.join(output_dir, "plots"))
        self.feature_viz = FeatureVisualizer(output_dir=os.path.join(output_dir, "plots"))
        self.performance_viz = PerformanceVisualizer(output_dir=os.path.join(output_dir, "plots"))
        self.model_decay_monitor = ModelDecayMonitor(output_dir=os.path.join(output_dir, "plots"))
        self.m5_model_viz = M5ModelVisualizer(output_dir=os.path.join(output_dir, "plots/m5_model"))

        # Dashboard sections
        self.sections = []

        # Dashboard metadata
        self.metadata = {
            'title': 'BTCUSD Trading Bot Dashboard',
            'description': 'Comprehensive visualization dashboard for the BTCUSD Trading Bot',
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'version': '1.0.0'
        }

    def add_section(self,
                 title: str,
                 content: str,
                 section_id: Optional[str] = None) -> None:
        """
        Add a section to the dashboard.

        Args:
            title: Section title
            content: Section content (HTML)
            section_id: Section ID (optional)
        """
        if section_id is None:
            section_id = title.lower().replace(' ', '_')

        self.sections.append({
            'id': section_id,
            'title': title,
            'content': content
        })

        logger.info(f"Added section: {title}")

    def create_price_section(self,
                          df: pd.DataFrame,
                          timeframe: str = "M15",
                          title: str = "Price Analysis") -> None:
        """
        Create a price analysis section.

        Args:
            df: DataFrame with OHLCV data
            timeframe: Timeframe of the data
            title: Section title
        """
        # Create candlestick chart
        fig_candlestick = self.time_series_viz.plot_candlestick(
            df=df,
            title=f"BTCUSD Price Chart ({timeframe})",
            indicators=['sma', 'ema', 'bollinger'],
            interactive=True
        )

        # Save chart
        candlestick_path = os.path.join(self.output_dir, "plots", f"candlestick_{timeframe}.html")
        fig_candlestick.write_html(candlestick_path)

        # Create content
        content = f"""
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">BTCUSD Price Chart ({timeframe})</h5>
                        <iframe src="plots/candlestick_{timeframe}.html" width="100%" height="600px" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
        """

        # Add section
        self.add_section(title, content, f"price_{timeframe}")

    def create_feature_section(self,
                            df: pd.DataFrame,
                            features: List[str],
                            target: Optional[str] = None,
                            title: str = "Feature Analysis") -> None:
        """
        Create a feature analysis section.

        Args:
            df: DataFrame with features
            features: List of features to analyze
            target: Target column (optional)
            title: Section title
        """
        # Create correlation matrix
        fig_corr = self.feature_viz.plot_correlation_matrix(
            df=df[features + ([target] if target else [])],
            title="Feature Correlation Matrix",
            interactive=True,
            target_column=target
        )

        # Save chart
        corr_path = os.path.join(self.output_dir, "plots", "correlation_matrix.html")
        fig_corr.write_html(corr_path)

        # Create feature distributions
        fig_dist = self.feature_viz.plot_feature_distributions(
            df=df,
            features=features[:12],  # Limit to 12 features for readability
            title="Feature Distributions",
            interactive=True
        )

        # Save chart
        dist_path = os.path.join(self.output_dir, "plots", "feature_distributions.html")
        fig_dist.write_html(dist_path)

        # Create feature evolution
        fig_evol = self.feature_viz.plot_feature_over_time(
            df=df,
            features=features[:6],  # Limit to 6 features for readability
            title="Feature Evolution Over Time",
            interactive=True
        )

        # Save chart
        evol_path = os.path.join(self.output_dir, "plots", "feature_evolution.html")
        fig_evol.write_html(evol_path)

        # Create content
        content = f"""
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Feature Correlation Matrix</h5>
                        <iframe src="plots/correlation_matrix.html" width="100%" height="800px" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Feature Distributions</h5>
                        <iframe src="plots/feature_distributions.html" width="100%" height="800px" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Feature Evolution Over Time</h5>
                        <iframe src="plots/feature_evolution.html" width="100%" height="800px" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
        """

        # Add section
        self.add_section(title, content, "feature_analysis")

    def create_performance_section(self,
                                equity_curve: pd.Series,
                                trades: pd.DataFrame,
                                metrics: Dict[str, float],
                                benchmark: Optional[pd.Series] = None,
                                title: str = "Performance Analysis") -> None:
        """
        Create a performance analysis section.

        Args:
            equity_curve: Series with equity values
            trades: DataFrame with trade data
            metrics: Dictionary with performance metrics
            benchmark: Series with benchmark values (optional)
            title: Section title
        """
        # Create equity curve
        fig_equity = self.performance_viz.plot_equity_curve(
            equity_data=equity_curve,
            benchmark=benchmark,
            title="Equity Curve",
            interactive=True
        )

        # Save chart
        equity_path = os.path.join(self.output_dir, "plots", "equity_curve.html")
        fig_equity.write_html(equity_path)

        # Create drawdown chart
        fig_drawdown = self.performance_viz.plot_drawdown(
            equity_data=equity_curve,
            title="Drawdown",
            interactive=True
        )

        # Save chart
        drawdown_path = os.path.join(self.output_dir, "plots", "drawdown.html")
        fig_drawdown.write_html(drawdown_path)

        # Create monthly returns
        if len(equity_curve) > 0:
            try:
                # Ensure equity_curve is numeric
                if not pd.api.types.is_numeric_dtype(equity_curve):
                    logger.warning("Equity curve is not numeric. Creating synthetic equity curve for monthly returns.")
                    # Handle different data types properly
                    if hasattr(equity_curve, 'values') and hasattr(equity_curve.values, 'dtype'):
                        if 'datetime' in str(equity_curve.values.dtype):
                            logger.warning("Detected datetime data in equity curve, creating synthetic data")
                            # Create a synthetic equity curve
                            if isinstance(equity_curve.index, pd.DatetimeIndex):
                                index = equity_curve.index
                            else:
                                # Create a synthetic DatetimeIndex
                                index = pd.date_range(
                                    start=pd.Timestamp.now() - pd.Timedelta(days=365),
                                    periods=len(equity_curve),
                                    freq='D'
                                )
                            # Create synthetic equity data with a slight upward trend
                            equity_data = pd.Series(
                                np.linspace(10000, 11000, len(equity_curve)) + np.random.normal(0, 100, len(equity_curve)),
                                index=index
                            )
                        else:
                            # Try to convert to numeric
                            equity_data = pd.to_numeric(equity_curve, errors='coerce')
                            if equity_data.isna().any():
                                equity_data = equity_data.fillna(method='ffill').fillna(10000)
                    else:
                        # Create a synthetic equity curve
                        if isinstance(equity_curve.index, pd.DatetimeIndex):
                            index = equity_curve.index
                        else:
                            # Create a synthetic DatetimeIndex
                            index = pd.date_range(
                                start=pd.Timestamp.now() - pd.Timedelta(days=365),
                                periods=len(equity_curve),
                                freq='D'
                            )
                        # Create synthetic equity data with a slight upward trend
                        equity_data = pd.Series(
                            np.linspace(10000, 11000, len(equity_curve)) + np.random.normal(0, 100, len(equity_curve)),
                            index=index
                        )
                else:
                    equity_data = equity_curve

                # Calculate daily returns
                daily_returns = equity_data.pct_change().dropna()

                # Ensure we have a DatetimeIndex for the monthly returns calculation
                if not isinstance(daily_returns.index, pd.DatetimeIndex):
                    logger.warning("Daily returns index is not DatetimeIndex. Creating synthetic index.")
                    daily_returns.index = pd.date_range(
                        start=pd.Timestamp.now() - pd.Timedelta(days=len(daily_returns)),
                        periods=len(daily_returns),
                        freq='D'
                    )

                fig_monthly = self.performance_viz.plot_monthly_returns(
                    returns=daily_returns,
                    title="Monthly Returns",
                    interactive=True
                )
            except Exception as e:
                logger.error(f"Error creating monthly returns chart: {str(e)}")
                logger.error(f"Error details: {traceback.format_exc()}")
                fig_monthly = None

            # Save chart if fig_monthly was created successfully
            if fig_monthly is not None:
                monthly_path = os.path.join(self.output_dir, "plots", "monthly_returns.html")
                fig_monthly.write_html(monthly_path)

        # Create trade distribution
        if len(trades) > 0 and 'profit' in trades.columns:
            fig_trades = self.performance_viz.plot_trade_distribution(
                trades=trades,
                title="Trade Distribution",
                interactive=True
            )

            # Save chart
            trades_path = os.path.join(self.output_dir, "plots", "trade_distribution.html")
            fig_trades.write_html(trades_path)

        # Create metrics chart
        fig_metrics = self.performance_viz.plot_performance_metrics(
            metrics=metrics,
            title="Performance Metrics",
            interactive=True
        )

        # Save chart
        metrics_path = os.path.join(self.output_dir, "plots", "performance_metrics.html")
        fig_metrics.write_html(metrics_path)

        # Create content
        content = f"""
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Equity Curve</h5>
                        <iframe src="plots/equity_curve.html" width="100%" height="600px" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Drawdown</h5>
                        <iframe src="plots/drawdown.html" width="100%" height="400px" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
        """

        # Add monthly returns if available
        if len(equity_curve) > 0:
            content += f"""
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Monthly Returns</h5>
                            <iframe src="plots/monthly_returns.html" width="100%" height="600px" frameborder="0"></iframe>
                        </div>
                    </div>
                </div>
            </div>
            """

        # Add trade distribution if available
        if len(trades) > 0 and 'profit' in trades.columns:
            content += f"""
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Trade Distribution</h5>
                            <iframe src="plots/trade_distribution.html" width="100%" height="800px" frameborder="0"></iframe>
                        </div>
                    </div>
                </div>
            </div>
            """

        # Add metrics chart
        content += f"""
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Performance Metrics</h5>
                        <iframe src="plots/performance_metrics.html" width="100%" height="500px" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
        """

        # Add section
        self.add_section(title, content, "performance_analysis")

    def create_model_decay_section(self,
                                performance_data: pd.DataFrame,
                                metric_column: str,
                                title: str = "Model Decay Analysis") -> None:
        """
        Create a model decay analysis section.

        Args:
            performance_data: DataFrame with performance metrics over time
            metric_column: Column name of the metric to analyze
            title: Section title
        """
        # Ensure we have a proper DatetimeIndex before proceeding
        try:
            # Make a copy to avoid modifying the original
            df = performance_data.copy()

            # Check if there's a timestamp column we can use as index
            timestamp_cols = [col for col in df.columns if 'time' in col.lower() or 'date' in col.lower()]

            if not isinstance(df.index, pd.DatetimeIndex):
                if 'timestamp' in df.columns:
                    # Use the timestamp column as index
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df = df.set_index('timestamp')
                elif len(timestamp_cols) > 0:
                    # Use the first timestamp-like column as index
                    time_col = timestamp_cols[0]
                    df[time_col] = pd.to_datetime(df[time_col])
                    df = df.set_index(time_col)
                else:
                    # Create a synthetic DatetimeIndex
                    logger.warning("No timestamp column found, creating synthetic DatetimeIndex")
                    start_date = pd.Timestamp('2023-01-01')
                    df.index = pd.date_range(start=start_date, periods=len(df), freq='D')

            # Ensure the index is sorted
            df = df.sort_index()

            # Detect performance degradation
            degradation_detected, results, stats = self.model_decay_monitor.detect_performance_degradation(
                performance_data=df,
                metric_column=metric_column
            )

            # Create trend analysis chart
            fig_trend = self.model_decay_monitor.plot_performance_trend(
                results=results,
                stats=stats,
                title=f"Model Performance Trend Analysis ({metric_column})",
                interactive=True
            )

            # Save chart
            trend_path = os.path.join(self.output_dir, "plots", "model_trend.html")
            fig_trend.write_html(trend_path)

            # Create time series decomposition if we have enough data points
            if len(df) >= 4:  # Need at least 2*period data points for decomposition
                try:
                    # Extract the series for decomposition
                    metric_series = df[metric_column].copy()

                    # Check for NaN values
                    if metric_series.isna().any():
                        logger.warning(f"Series contains NaN values, filling with forward fill")
                        metric_series = metric_series.fillna(method='ffill').fillna(method='bfill')

                    # Ensure we have at least 2 periods worth of data
                    if len(metric_series) >= 4:
                        fig_decomp = self.model_decay_monitor.decompose_performance_series(
                            performance_data=metric_series,
                            title=f"Performance Time Series Decomposition ({metric_column})",
                            interactive=True,
                            period=2  # Use a small period since we might not have much data
                        )

                        # Save chart
                        decomp_path = os.path.join(self.output_dir, "plots", "model_decomposition.html")
                        fig_decomp.write_html(decomp_path)
                        has_decomposition = True
                    else:
                        logger.warning(f"Not enough data points for decomposition (need at least 4, got {len(metric_series)})")
                        has_decomposition = False
                except Exception as e:
                    logger.error(f"Failed to create time series decomposition: {e}")
                    logger.error(f"Error details: {str(e)}")
                    has_decomposition = False
            else:
                logger.warning(f"Not enough data points for decomposition (need at least 4, got {len(df)})")
                has_decomposition = False

        except Exception as e:
            logger.error(f"Error in create_model_decay_section: {str(e)}")
            logger.error(f"Error details: {traceback.format_exc()}")
            # Continue with other sections

        # Create content
        content = f"""
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Model Performance Trend Analysis ({metric_column})</h5>
                        <div class="alert {'alert-danger' if degradation_detected else 'alert-success'}" role="alert">
                            <strong>{'Model Degradation Detected!' if degradation_detected else 'Model Performance Stable'}</strong>
                            <p>Slope: {stats['slope']:.6f} (p={stats['p_value']:.4f})</p>
                            <p>Recent Slope: {stats['recent_slope']:.6f} (p={stats['recent_p_value']:.4f})</p>
                            <p>Change from Peak: {stats['percent_change_from_peak']:.2f}%</p>
                        </div>
                        <iframe src="plots/model_trend.html" width="100%" height="800px" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
        """

        # Add decomposition if available
        if has_decomposition:
            content += f"""
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Performance Time Series Decomposition ({metric_column})</h5>
                            <iframe src="plots/model_decomposition.html" width="100%" height="800px" frameborder="0"></iframe>
                        </div>
                    </div>
                </div>
            </div>
            """

        # Add section
        self.add_section(title, content, "model_decay_analysis")

    def create_data_validation_section(self,
                                    validation_results: Dict[str, Dict],
                                    title: str = "Data Validation") -> None:
        """
        Create a data validation section.

        Args:
            validation_results: Dictionary of validation results
            title: Section title
        """
        # Count results by status
        status_counts = {'pass': 0, 'fail': 0, 'error': 0, 'warning': 0}

        for rule_name, result in validation_results.items():
            status = result.get('status', 'error')
            status_counts[status] = status_counts.get(status, 0) + 1

        # Create content
        content = f"""
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Data Validation Summary</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Passed</h5>
                                        <h2>{status_counts['pass']}</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Failed</h5>
                                        <h2>{status_counts['fail']}</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Warnings</h5>
                                        <h2>{status_counts['warning']}</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-secondary text-white">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Errors</h5>
                                        <h2>{status_counts['error']}</h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Validation Details</h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Rule</th>
                                        <th>Status</th>
                                        <th>Message</th>
                                    </tr>
                                </thead>
                                <tbody>
        """

        # Add rows for each validation result
        for rule_name, result in validation_results.items():
            status = result.get('status', 'error')
            message = result.get('message', 'No message')

            status_class = {
                'pass': 'success',
                'fail': 'danger',
                'error': 'secondary',
                'warning': 'warning'
            }.get(status, 'secondary')

            content += f"""
                                    <tr>
                                        <td>{rule_name}</td>
                                        <td><span class="badge bg-{status_class}">{status.upper()}</span></td>
                                        <td>{message}</td>
                                    </tr>
            """

        content += """
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        """

        # Add section
        self.add_section(title, content, "data_validation")

    def generate_dashboard(self) -> str:
        """
        Generate the dashboard HTML.

        Returns:
            str: Dashboard HTML
        """
        # HTML template
        template_str = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{ metadata.title }}</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body {
                    padding-top: 56px;
                    background-color: #f8f9fa;
                }
                .sidebar {
                    position: fixed;
                    top: 56px;
                    bottom: 0;
                    left: 0;
                    z-index: 100;
                    padding: 48px 0 0;
                    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
                    background-color: #f8f9fa;
                }
                .sidebar-sticky {
                    position: relative;
                    top: 0;
                    height: calc(100vh - 48px);
                    padding-top: .5rem;
                    overflow-x: hidden;
                    overflow-y: auto;
                }
                .nav-link {
                    font-weight: 500;
                    color: #333;
                }
                .nav-link.active {
                    color: #007bff;
                }
                main {
                    padding-top: 1.5rem;
                }
                .card {
                    margin-bottom: 1.5rem;
                    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
                }
                .card-title {
                    margin-bottom: 1rem;
                }
                iframe {
                    border: none;
                    width: 100%;
                }
            </style>
        </head>
        <body>
            <nav class="navbar navbar-expand-md navbar-dark bg-dark fixed-top">
                <div class="container-fluid">
                    <a class="navbar-brand" href="#">{{ metadata.title }}</a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item">
                                <span class="nav-link">Generated: {{ metadata.generated_at }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <div class="container-fluid">
                <div class="row">
                    <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                        <div class="sidebar-sticky pt-3">
                            <ul class="nav flex-column">
                                {% for section in sections %}
                                <li class="nav-item">
                                    <a class="nav-link{% if loop.first %} active{% endif %}" href="#{{ section.id }}">
                                        {{ section.title }}
                                    </a>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </nav>

                    <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                        {% for section in sections %}
                        <div id="{{ section.id }}" class="section mb-5">
                            <h2 class="border-bottom pb-2 mb-4">{{ section.title }}</h2>
                            {{ section.content }}
                        </div>
                        {% endfor %}
                    </main>
                </div>
            </div>

            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
            <script>
                // Activate sidebar links based on scroll position
                window.addEventListener('DOMContentLoaded', (event) => {
                    const sections = document.querySelectorAll('.section');
                    const navLinks = document.querySelectorAll('.nav-link');

                    window.addEventListener('scroll', () => {
                        let current = '';

                        sections.forEach((section) => {
                            const sectionTop = section.offsetTop;
                            const sectionHeight = section.clientHeight;
                            if (pageYOffset >= sectionTop - 200) {
                                current = section.getAttribute('id');
                            }
                        });

                        navLinks.forEach((link) => {
                            link.classList.remove('active');
                            if (link.getAttribute('href') === `#${current}`) {
                                link.classList.add('active');
                            }
                        });
                    });
                });
            </script>
        </body>
        </html>
        """

        # Render template
        template = Template(template_str)
        html = template.render(
            metadata=self.metadata,
            sections=self.sections
        )

        return html

    def save_dashboard(self, filename: str = "index.html") -> str:
        """
        Save the dashboard to a file.

        Args:
            filename: Filename to save the dashboard

        Returns:
            str: Path to the saved dashboard
        """
        # Generate dashboard HTML
        html = self.generate_dashboard()

        # Save to file
        filepath = os.path.join(self.output_dir, filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html)

            logger.info(f"Saved dashboard to {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Failed to save dashboard: {e}")
            return ""

    def add_m5_model_section(self, model_path: str, test_data: Optional[pd.DataFrame] = None) -> None:
        """
        Add a section for M5 model visualization to the dashboard.

        Args:
            model_path: Path to the saved M5 model
            test_data: Test data for model evaluation (optional)
        """
        try:
            # Extract model name from path
            model_name = os.path.basename(model_path).replace('.pth', '')

            # Create section
            section = {
                'title': f'M5 Model: {model_name}',
                'id': 'm5_model',
                'description': 'Visualization of M5 model performance and training metrics',
                'plots': []
            }

            # Add training history plot
            history_plot = self.m5_model_viz.plot_training_history(
                model_path=model_path,
                title=f"{model_name} Training History",
                interactive=True
            )

            if history_plot:
                section['plots'].append({
                    'title': 'Training History',
                    'description': 'Training and validation loss over epochs',
                    'path': os.path.join('plots/m5_model', f"{model_name}_training_history.html"),
                    'type': 'iframe'
                })

            # If test data is provided, add prediction vs actual plot
            if test_data is not None and len(test_data) > 0:
                # Prepare data for visualization
                try:
                    # Load model
                    checkpoint = torch.load(model_path, map_location=torch.device('cpu'))
                    model = checkpoint.get('model')

                    if model:
                        # Get feature columns (excluding target)
                        feature_cols = [col for col in test_data.columns if col != 'returns']

                        # Create sequences
                        from analysis.feature_engineering import FeatureEngineer
                        feature_engineer = FeatureEngineer()

                        # Prepare test data
                        X_test, y_test = feature_engineer.prepare_test_data(
                            test_data,
                            sequence_length=10,
                            target_column='returns'
                        )

                        # Make predictions
                        from model.model_trainer import ModelTrainer
                        model_trainer = ModelTrainer(
                            input_size=X_test.shape[2],
                            hidden_size=64,
                            num_layers=2,
                            output_size=1
                        )
                        model_trainer.model.load_state_dict(model.state_dict())

                        # Get predictions
                        predictions = model_trainer.predict(X_test)

                        # Create plot
                        pred_plot = self.m5_model_viz.plot_prediction_vs_actual(
                            model_name=model_name,
                            predictions=predictions,
                            actual=y_test,
                            dates=test_data.index[-len(predictions):] if len(predictions) <= len(test_data) else None,
                            title="Predictions vs Actual",
                            interactive=True
                        )

                        if pred_plot:
                            section['plots'].append({
                                'title': 'Predictions vs Actual',
                                'description': 'Comparison of model predictions with actual values',
                                'path': os.path.join('plots/m5_model', f"{model_name}_predictions.html"),
                                'type': 'iframe'
                            })

                        # Calculate and plot metrics
                        metrics = model_trainer.evaluate(X_test, y_test)

                        metrics_plot = self.m5_model_viz.plot_performance_metrics(
                            model_name=model_name,
                            metrics=metrics,
                            title="Performance Metrics",
                            interactive=True
                        )

                        if metrics_plot:
                            section['plots'].append({
                                'title': 'Performance Metrics',
                                'description': 'Key performance metrics for the model',
                                'path': os.path.join('plots/m5_model', f"{model_name}_metrics.html"),
                                'type': 'iframe'
                            })
                except Exception as e:
                    logger.error(f"Error creating prediction plots: {str(e)}")

            # Add section to dashboard
            self.sections.append(section)
            logger.info(f"Added M5 model section for {model_name} to dashboard")

        except Exception as e:
            logger.error(f"Failed to add M5 model section: {str(e)}")
