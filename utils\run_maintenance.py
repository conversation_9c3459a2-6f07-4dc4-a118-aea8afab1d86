#!/usr/bin/env python3
"""
Helper script to run MT5 Algo Trading maintenance.
Used by the optimized batch file launcher.
"""

import sys
import os
import time
import logging
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_maintenance():
    """Run MT5 Algo Trading maintenance service."""
    # Set up logging to console
    logging.basicConfig(
        level=logging.INFO,
        format='[%(asctime)s] %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )

    try:
        from utils.algo_trading_manager import keep_algo_trading_enabled, verify_all_terminals
        from config.credentials import MT5_TERMINALS
        
        print('[INFO] Starting continuous Algo Trading maintenance...')
        print('[INFO] Monitoring terminals:', list(MT5_TERMINALS.keys()))
        print('[INFO] Press Ctrl+C to stop')
        print()
        
        # Run the maintenance function
        keep_algo_trading_enabled()
        
    except KeyboardInterrupt:
        print()
        print('[INFO] Maintenance service stopped by user')
        print('[INFO] Algo Trading status may need manual monitoring')
        
    except Exception as e:
        print(f'[ERROR] Error in maintenance service: {str(e)}')
        import traceback
        traceback.print_exc()
        print()
        print('[ERROR] Maintenance service failed')
        print('[ERROR] Please check the error above and restart')
        
    finally:
        print()
        print('[INFO] Algo Trading maintenance service ended')
        print('[WARNING] MT5 terminals may lose Algo Trading status without maintenance')
        input('Press Enter to close this window...')

if __name__ == "__main__":
    run_maintenance()
