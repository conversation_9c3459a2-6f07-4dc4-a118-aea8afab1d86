#!/usr/bin/env python3
"""
Helper script to check MT5 terminal configurations.
Used by the optimized batch file launcher.
"""

import sys
import os

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_terminals():
    """Check MT5 terminal configurations and report status."""
    try:
        from config.credentials import MT5_TERMINALS
        
        missing_terminals = []
        for terminal_id, config in MT5_TERMINALS.items():
            path = config.get('path', '')
            if not os.path.exists(path):
                missing_terminals.append(f'Terminal {terminal_id}: {path}')
        
        if missing_terminals:
            print('[WARNING] Some MT5 terminals not found:')
            for terminal in missing_terminals:
                print(f'  {terminal}')
            print('[WARNING] These terminals will not be available for trading')
        else:
            print('[SUCCESS] All MT5 terminals found')
            
        print(f'[INFO] Configured terminals: {len(MT5_TERMINALS)}')
        for terminal_id, config in MT5_TERMINALS.items():
            name = config.get('name', f'Terminal {terminal_id}')
            timeframe = config.get('timeframe', 'Unknown')
            print(f'  Terminal {terminal_id}: {name} ({timeframe})')
            
        return len(missing_terminals) == 0
        
    except Exception as e:
        print(f'[ERROR] Error checking MT5 terminals: {str(e)}')
        return False

if __name__ == "__main__":
    success = check_terminals()
    sys.exit(0 if success else 1)
