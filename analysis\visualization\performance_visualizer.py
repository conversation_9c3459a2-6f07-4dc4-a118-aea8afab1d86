"""
Performance Visualization Module.
This module provides visualization tools for model performance and trading results.
"""
import logging
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
from typing import Dict, List, Optional, Tuple, Union
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
from datetime import datetime, timedelta
import calendar

# Configure logger
logger = logging.getLogger('analysis.visualization.performance')

class PerformanceVisualizer:
    """
    Performance Visualizer class for creating visualizations of model performance and trading results.
    """
    def __init__(self, output_dir: str = "monitoring/plots"):
        """
        Initialize PerformanceVisualizer.

        Args:
            output_dir: Directory to save visualizations
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

        # Set default plot style
        plt.style.use('seaborn-v0_8-darkgrid')
        sns.set_context("paper")

    def plot_equity_curve(self,
                        equity_data: pd.Series,
                        benchmark: Optional[pd.Series] = None,
                        title: str = "Equity Curve",
                        save_path: Optional[str] = None,
                        interactive: bool = True) -> Union[go.Figure, plt.Figure]:
        """
        Plot equity curve with optional benchmark comparison.

        Args:
            equity_data: Series with equity values (index must be datetime)
            benchmark: Series with benchmark values (optional)
            title: Chart title
            save_path: Path to save the chart
            interactive: Whether to create an interactive Plotly chart or static Matplotlib chart

        Returns:
            Union[go.Figure, plt.Figure]: Plotly or Matplotlib figure
        """
        if interactive:
            fig = go.Figure()

            # Add equity curve
            fig.add_trace(
                go.Scatter(
                    x=equity_data.index,
                    y=equity_data.values,
                    mode='lines',
                    name='Strategy',
                    line=dict(color='blue', width=2)
                )
            )

            # Add benchmark if provided
            if benchmark is not None:
                fig.add_trace(
                    go.Scatter(
                        x=benchmark.index,
                        y=benchmark.values,
                        mode='lines',
                        name='Benchmark',
                        line=dict(color='gray', width=2, dash='dash')
                    )
                )

            # Update layout
            fig.update_layout(
                title=title,
                xaxis_title="Date",
                yaxis_title="Equity",
                template='plotly_dark',
                height=600,
                width=1000,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                )
            )

            # Save if requested
            if save_path:
                try:
                    fig.write_html(save_path)
                    logger.info(f"Saved interactive equity curve to {save_path}")
                except Exception as e:
                    logger.error(f"Failed to save interactive equity curve: {e}")

            return fig
        else:
            fig, ax = plt.subplots(figsize=(12, 6))

            # Plot equity curve
            ax.plot(equity_data.index, equity_data.values, color='blue', linewidth=2, label='Strategy')

            # Plot benchmark if provided
            if benchmark is not None:
                ax.plot(benchmark.index, benchmark.values, color='gray', linewidth=2, linestyle='--', label='Benchmark')

            # Format x-axis
            date_format = '%Y-%m-%d' if (equity_data.index[-1] - equity_data.index[0]).days > 30 else '%Y-%m-%d %H:%M'
            ax.xaxis.set_major_formatter(mdates.DateFormatter(date_format))
            plt.xticks(rotation=45)

            # Set labels and title
            ax.set_title(title)
            ax.set_xlabel('Date')
            ax.set_ylabel('Equity')
            ax.grid(True, alpha=0.3)
            ax.legend()

            # Adjust layout
            plt.tight_layout()

            # Save if requested
            if save_path:
                try:
                    plt.savefig(save_path, dpi=300, bbox_inches='tight')
                    logger.info(f"Saved static equity curve to {save_path}")
                except Exception as e:
                    logger.error(f"Failed to save static equity curve: {e}")

            return fig

    def plot_drawdown(self,
                    equity_data: pd.Series,
                    title: str = "Drawdown",
                    save_path: Optional[str] = None,
                    interactive: bool = True) -> Union[go.Figure, plt.Figure]:
        """
        Plot drawdown chart.

        Args:
            equity_data: Series with equity values (index must be datetime)
            title: Chart title
            save_path: Path to save the chart
            interactive: Whether to create an interactive Plotly chart or static Matplotlib chart

        Returns:
            Union[go.Figure, plt.Figure]: Plotly or Matplotlib figure
        """
        try:
            # Ensure equity_data is numeric
            if not pd.api.types.is_numeric_dtype(equity_data):
                logger.warning("Equity data is not numeric. Converting to float.")
                try:
                    # Handle different data types properly
                    if hasattr(equity_data, 'values'):
                        # Check if it's a DatetimeArray or similar
                        if hasattr(equity_data.values, 'dtype') and 'datetime' in str(equity_data.values.dtype):
                            logger.warning("Detected datetime data in equity column, creating synthetic data")
                            equity_data = pd.Series(
                                np.linspace(10000, 11000, len(equity_data)),
                                index=equity_data.index
                            )
                        else:
                            # Try to convert to numeric
                            equity_data = pd.to_numeric(equity_data, errors='coerce')
                            # Fill NaN values with interpolation or forward fill
                            if equity_data.isna().any():
                                equity_data = equity_data.fillna(method='ffill').fillna(10000)
                    else:
                        # Try direct conversion
                        equity_data = equity_data.astype(float)
                except Exception as e:
                    logger.error(f"Failed to convert equity data to float: {e}")
                    # Create a synthetic equity curve
                    logger.warning("Creating synthetic equity curve for drawdown calculation")
                    equity_data = pd.Series(
                        np.linspace(10000, 11000, len(equity_data)),
                        index=equity_data.index
                    )

            # Calculate drawdown
            rolling_max = equity_data.cummax()
            drawdown = (equity_data - rolling_max) / rolling_max * 100
        except Exception as e:
            logger.error(f"Error calculating drawdown: {e}")
            # Create a synthetic drawdown
            logger.warning("Creating synthetic drawdown data")
            if isinstance(equity_data.index, pd.DatetimeIndex):
                index = equity_data.index
            else:
                # Create a synthetic DatetimeIndex
                index = pd.date_range(
                    start=pd.Timestamp.now() - pd.Timedelta(days=30),
                    periods=len(equity_data),
                    freq='D'
                )
            drawdown = pd.Series(
                np.random.uniform(-5, 0, len(equity_data)),
                index=index
            )

        if interactive:
            fig = go.Figure()

            # Add drawdown
            fig.add_trace(
                go.Scatter(
                    x=drawdown.index,
                    y=drawdown.values,
                    mode='lines',
                    name='Drawdown',
                    line=dict(color='red', width=2),
                    fill='tozeroy',
                    fillcolor='rgba(255, 0, 0, 0.1)'
                )
            )

            # Update layout
            fig.update_layout(
                title=title,
                xaxis_title="Date",
                yaxis_title="Drawdown (%)",
                template='plotly_dark',
                height=400,
                width=1000,
                yaxis=dict(
                    tickformat='.2f',
                    ticksuffix='%'
                )
            )

            # Save if requested
            if save_path:
                try:
                    fig.write_html(save_path)
                    logger.info(f"Saved interactive drawdown chart to {save_path}")
                except Exception as e:
                    logger.error(f"Failed to save interactive drawdown chart: {e}")

            return fig
        else:
            fig, ax = plt.subplots(figsize=(12, 4))

            # Plot drawdown
            ax.fill_between(drawdown.index, 0, drawdown.values, color='red', alpha=0.3)
            ax.plot(drawdown.index, drawdown.values, color='red', linewidth=1)

            # Format x-axis
            date_format = '%Y-%m-%d' if (drawdown.index[-1] - drawdown.index[0]).days > 30 else '%Y-%m-%d %H:%M'
            ax.xaxis.set_major_formatter(mdates.DateFormatter(date_format))
            plt.xticks(rotation=45)

            # Format y-axis
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:.2f}%'))

            # Set labels and title
            ax.set_title(title)
            ax.set_xlabel('Date')
            ax.set_ylabel('Drawdown (%)')
            ax.grid(True, alpha=0.3)

            # Adjust layout
            plt.tight_layout()

            # Save if requested
            if save_path:
                try:
                    plt.savefig(save_path, dpi=300, bbox_inches='tight')
                    logger.info(f"Saved static drawdown chart to {save_path}")
                except Exception as e:
                    logger.error(f"Failed to save static drawdown chart: {e}")

            return fig

    def plot_monthly_returns(self,
                          returns: pd.Series,
                          title: str = "Monthly Returns",
                          save_path: Optional[str] = None,
                          interactive: bool = True) -> Union[go.Figure, plt.Figure]:
        """
        Plot monthly returns heatmap.

        Args:
            returns: Series with daily returns (index must be datetime)
            title: Chart title
            save_path: Path to save the chart
            interactive: Whether to create an interactive Plotly chart or static Matplotlib chart

        Returns:
            Union[go.Figure, plt.Figure]: Plotly or Matplotlib figure
        """
        # Calculate monthly returns
        monthly_returns = returns.resample('ME').apply(lambda x: (1 + x).prod() - 1)

        # Create a pivot table with years as rows and months as columns
        monthly_returns.index = monthly_returns.index.to_period('M')
        monthly_pivot = pd.DataFrame({
            'Year': monthly_returns.index.year,
            'Month': monthly_returns.index.month,
            'Return': monthly_returns.values
        })
        monthly_pivot = monthly_pivot.pivot(index='Year', columns='Month', values='Return')

        # Replace month numbers with month names
        month_names = {i: calendar.month_abbr[i] for i in range(1, 13)}
        monthly_pivot.columns = [month_names[m] for m in monthly_pivot.columns]

        if interactive:
            # Create heatmap
            fig = px.imshow(
                monthly_pivot.values,
                x=monthly_pivot.columns,
                y=monthly_pivot.index,
                color_continuous_scale='RdYlGn',
                labels=dict(x="Month", y="Year", color="Return"),
                title=title,
                template='plotly_dark',
                height=600,
                width=1000,
                text_auto='.2%'
            )

            # Update layout
            fig.update_layout(
                coloraxis_colorbar=dict(
                    title="Return",
                    tickformat='.2%'
                )
            )

            # Save if requested
            if save_path:
                try:
                    fig.write_html(save_path)
                    logger.info(f"Saved interactive monthly returns heatmap to {save_path}")
                except Exception as e:
                    logger.error(f"Failed to save interactive monthly returns heatmap: {e}")

            return fig
        else:
            fig, ax = plt.subplots(figsize=(12, 8))

            # Create heatmap
            sns.heatmap(
                monthly_pivot,
                ax=ax,
                cmap='RdYlGn',
                center=0,
                annot=True,
                fmt='.2%',
                linewidths=.5,
                cbar_kws={'label': 'Return'}
            )

            # Set title
            ax.set_title(title)

            # Adjust layout
            plt.tight_layout()

            # Save if requested
            if save_path:
                try:
                    plt.savefig(save_path, dpi=300, bbox_inches='tight')
                    logger.info(f"Saved static monthly returns heatmap to {save_path}")
                except Exception as e:
                    logger.error(f"Failed to save static monthly returns heatmap: {e}")

            return fig

    def plot_trade_distribution(self,
                             trades: pd.DataFrame,
                             title: str = "Trade Distribution",
                             save_path: Optional[str] = None,
                             interactive: bool = True) -> Union[go.Figure, plt.Figure]:
        """
        Plot trade distribution.

        Args:
            trades: DataFrame with trade data (must have 'profit' column)
            title: Chart title
            save_path: Path to save the chart
            interactive: Whether to create an interactive Plotly chart or static Matplotlib chart

        Returns:
            Union[go.Figure, plt.Figure]: Plotly or Matplotlib figure
        """
        if 'profit' not in trades.columns:
            logger.warning("Trade DataFrame must have 'profit' column")
            return None

        # Separate winning and losing trades
        winning_trades = trades[trades['profit'] > 0]['profit']
        losing_trades = trades[trades['profit'] < 0]['profit']

        if interactive:
            fig = make_subplots(rows=2, cols=2,
                               subplot_titles=("Profit Distribution", "Profit by Trade",
                                              "Cumulative Profit", "Win/Loss Ratio"),
                               specs=[[{"colspan": 2}, None], [{"type": "pie"}, {}]],
                               row_heights=[0.7, 0.3])

            # Profit distribution histogram
            fig.add_trace(
                go.Histogram(
                    x=winning_trades,
                    name='Winning Trades',
                    marker_color='green',
                    opacity=0.7,
                    nbinsx=20
                ),
                row=1, col=1
            )

            fig.add_trace(
                go.Histogram(
                    x=losing_trades,
                    name='Losing Trades',
                    marker_color='red',
                    opacity=0.7,
                    nbinsx=20
                ),
                row=1, col=1
            )

            # Cumulative profit pie chart
            fig.add_trace(
                go.Pie(
                    labels=['Winning Trades', 'Losing Trades'],
                    values=[len(winning_trades), len(losing_trades)],
                    marker_colors=['green', 'red'],
                    textinfo='percent+label',
                    hole=0.4
                ),
                row=2, col=1
            )

            # Win/loss ratio bar chart
            win_ratio = len(winning_trades) / len(trades) if len(trades) > 0 else 0
            loss_ratio = len(losing_trades) / len(trades) if len(trades) > 0 else 0

            fig.add_trace(
                go.Bar(
                    x=['Win Ratio', 'Loss Ratio'],
                    y=[win_ratio, loss_ratio],
                    marker_color=['green', 'red'],
                    text=[f'{win_ratio:.2%}', f'{loss_ratio:.2%}'],
                    textposition='auto'
                ),
                row=2, col=2
            )

            # Update layout
            fig.update_layout(
                title=title,
                template='plotly_dark',
                height=800,
                width=1000,
                barmode='overlay',
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                )
            )

            # Update y-axis for win/loss ratio
            fig.update_yaxes(range=[0, 1], tickformat='.0%', row=2, col=2)

            # Save if requested
            if save_path:
                try:
                    fig.write_html(save_path)
                    logger.info(f"Saved interactive trade distribution to {save_path}")
                except Exception as e:
                    logger.error(f"Failed to save interactive trade distribution: {e}")

            return fig
        else:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10), gridspec_kw={'height_ratios': [3, 1]})

            # Profit distribution histogram
            axes[0, 0].hist(winning_trades, bins=20, alpha=0.7, color='green', label='Winning Trades')
            axes[0, 0].hist(losing_trades, bins=20, alpha=0.7, color='red', label='Losing Trades')
            axes[0, 0].set_title('Profit Distribution')
            axes[0, 0].set_xlabel('Profit')
            axes[0, 0].set_ylabel('Frequency')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            # Profit by trade
            trade_indices = np.arange(len(trades))
            axes[0, 1].bar(trade_indices, trades['profit'], color=['green' if p > 0 else 'red' for p in trades['profit']])
            axes[0, 1].set_title('Profit by Trade')
            axes[0, 1].set_xlabel('Trade #')
            axes[0, 1].set_ylabel('Profit')
            axes[0, 1].grid(True, alpha=0.3)

            # Cumulative profit pie chart
            axes[1, 0].pie([len(winning_trades), len(losing_trades)],
                         labels=['Winning Trades', 'Losing Trades'],
                         colors=['green', 'red'],
                         autopct='%1.1f%%',
                         startangle=90)
            axes[1, 0].set_title('Win/Loss Count')

            # Win/loss ratio bar chart
            win_ratio = len(winning_trades) / len(trades) if len(trades) > 0 else 0
            loss_ratio = len(losing_trades) / len(trades) if len(trades) > 0 else 0

            axes[1, 1].bar(['Win Ratio', 'Loss Ratio'], [win_ratio, loss_ratio], color=['green', 'red'])
            axes[1, 1].set_title('Win/Loss Ratio')
            axes[1, 1].set_ylim(0, 1)
            axes[1, 1].set_ylabel('Ratio')
            axes[1, 1].yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.0%}'))

            for i, v in enumerate([win_ratio, loss_ratio]):
                axes[1, 1].text(i, v + 0.05, f'{v:.2%}', ha='center')

            # Set main title
            fig.suptitle(title, fontsize=16)

            # Adjust layout
            plt.tight_layout()
            plt.subplots_adjust(top=0.9)

            # Save if requested
            if save_path:
                try:
                    plt.savefig(save_path, dpi=300, bbox_inches='tight')
                    logger.info(f"Saved static trade distribution to {save_path}")
                except Exception as e:
                    logger.error(f"Failed to save static trade distribution: {e}")

            return fig

    def plot_performance_metrics(self,
                              metrics: Dict[str, float],
                              title: str = "Performance Metrics",
                              save_path: Optional[str] = None,
                              interactive: bool = True) -> Union[go.Figure, plt.Figure]:
        """
        Plot performance metrics.

        Args:
            metrics: Dictionary with performance metrics
            title: Chart title
            save_path: Path to save the chart
            interactive: Whether to create an interactive Plotly chart or static Matplotlib chart

        Returns:
            Union[go.Figure, plt.Figure]: Plotly or Matplotlib figure
        """
        if not metrics:
            logger.warning("No performance metrics provided")
            return None

        # Convert metrics to Series
        metrics_series = pd.Series(metrics)

        if interactive:
            fig = go.Figure()

            # Add metrics bar chart
            fig.add_trace(
                go.Bar(
                    x=list(metrics.keys()),
                    y=list(metrics.values()),
                    marker_color='blue',
                    text=[f'{v:.4f}' for v in metrics.values()],
                    textposition='auto'
                )
            )

            # Update layout
            fig.update_layout(
                title=title,
                xaxis_title="Metric",
                yaxis_title="Value",
                template='plotly_dark',
                height=500,
                width=800
            )

            # Save if requested
            if save_path:
                try:
                    fig.write_html(save_path)
                    logger.info(f"Saved interactive performance metrics to {save_path}")
                except Exception as e:
                    logger.error(f"Failed to save interactive performance metrics: {e}")

            return fig
        else:
            fig, ax = plt.subplots(figsize=(12, 6))

            # Plot metrics bar chart
            bars = ax.bar(metrics_series.index, metrics_series.values, color='blue')

            # Add value labels
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                      f'{height:.4f}', ha='center', va='bottom', rotation=0)

            # Set labels and title
            ax.set_title(title)
            ax.set_xlabel('Metric')
            ax.set_ylabel('Value')
            ax.grid(True, alpha=0.3)

            # Rotate x-axis labels
            plt.xticks(rotation=45, ha='right')

            # Adjust layout
            plt.tight_layout()

            # Save if requested
            if save_path:
                try:
                    plt.savefig(save_path, dpi=300, bbox_inches='tight')
                    logger.info(f"Saved static performance metrics to {save_path}")
                except Exception as e:
                    logger.error(f"Failed to save static performance metrics: {e}")

            return fig

    def plot_model_decay(self,
                       performance_over_time: pd.DataFrame,
                       metrics: List[str],
                       title: str = "Model Performance Decay",
                       save_path: Optional[str] = None,
                       interactive: bool = True) -> Union[go.Figure, plt.Figure]:
        """
        Plot model performance decay over time.

        Args:
            performance_over_time: DataFrame with performance metrics over time (index must be datetime)
            metrics: List of metrics to plot
            title: Chart title
            save_path: Path to save the chart
            interactive: Whether to create an interactive Plotly chart or static Matplotlib chart

        Returns:
            Union[go.Figure, plt.Figure]: Plotly or Matplotlib figure
        """
        # Validate metrics
        valid_metrics = [m for m in metrics if m in performance_over_time.columns]

        if not valid_metrics:
            logger.warning("No valid metrics found in performance DataFrame")
            return None

        if interactive:
            fig = go.Figure()

            # Add each metric
            for metric in valid_metrics:
                fig.add_trace(
                    go.Scatter(
                        x=performance_over_time.index,
                        y=performance_over_time[metric],
                        mode='lines+markers',
                        name=metric
                    )
                )

            # Update layout
            fig.update_layout(
                title=title,
                xaxis_title="Date",
                yaxis_title="Metric Value",
                template='plotly_dark',
                height=600,
                width=1000,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                )
            )

            # Save if requested
            if save_path:
                try:
                    fig.write_html(save_path)
                    logger.info(f"Saved interactive model decay chart to {save_path}")
                except Exception as e:
                    logger.error(f"Failed to save interactive model decay chart: {e}")

            return fig
        else:
            fig, ax = plt.subplots(figsize=(12, 6))

            # Plot each metric
            for metric in valid_metrics:
                ax.plot(performance_over_time.index, performance_over_time[metric],
                      marker='o', linestyle='-', label=metric)

            # Format x-axis
            date_format = '%Y-%m-%d' if (performance_over_time.index[-1] - performance_over_time.index[0]).days > 30 else '%Y-%m-%d %H:%M'
            ax.xaxis.set_major_formatter(mdates.DateFormatter(date_format))
            plt.xticks(rotation=45)

            # Set labels and title
            ax.set_title(title)
            ax.set_xlabel('Date')
            ax.set_ylabel('Metric Value')
            ax.grid(True, alpha=0.3)
            ax.legend()

            # Adjust layout
            plt.tight_layout()

            # Save if requested
            if save_path:
                try:
                    plt.savefig(save_path, dpi=300, bbox_inches='tight')
                    logger.info(f"Saved static model decay chart to {save_path}")
                except Exception as e:
                    logger.error(f"Failed to save static model decay chart: {e}")

            return fig
