"""
Dashboard Module.
This module provides a real-time dashboard for monitoring the trading bot.
"""
import logging
import time
import os
import traceback
from typing import Dict, List, Optional, Tuple, Union
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.ticker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
import seaborn as sns
from datetime import datetime, timedelta
import threading
import webbrowser
from monitoring.performance_monitor import PerformanceMonitor

# Import visualization modules
from analysis.visualization.dashboard import VisualizationDashboard
from analysis.visualization.time_series_visualizer import TimeSeriesVisualizer
from analysis.visualization.feature_visualizer import FeatureVisualizer
from analysis.visualization.performance_visualizer import PerformanceVisualizer
from analysis.visualization.model_decay_monitor import ModelDecayMonitor

# Import validation modules
from data.validation.data_validator import DataValidator
from data.validation.feature_validator import FeatureValidator
from data.validation.model_validator import ModelValidator

# Configure logger
logger = logging.getLogger('monitoring.dashboard')

class Dashboard:
    """
    Dashboard class for real-time monitoring of the trading bot.
    """
    def __init__(self, performance_monitor: PerformanceMonitor):
        """
        Initialize Dashboard.

        Args:
            performance_monitor: PerformanceMonitor instance
        """
        self.performance_monitor = performance_monitor
        self.dashboard_dir = 'monitoring/dashboard'
        self.update_interval = 60  # Update interval in seconds
        self.is_running = False
        self.update_thread = None

        # Create dashboard directory
        os.makedirs(self.dashboard_dir, exist_ok=True)

        # Initialize visualization dashboard
        self.viz_dashboard = VisualizationDashboard(output_dir=self.dashboard_dir)

        # Initialize validators
        self.data_validator = DataValidator()
        self.feature_validator = FeatureValidator()
        self.model_validator = ModelValidator()

    def start(self):
        """Start the dashboard."""
        if self.is_running:
            logger.warning("Dashboard is already running")
            return

        self.is_running = True

        # Generate initial dashboard in the main thread
        self.generate_dashboard()

        # Open dashboard in browser
        dashboard_path = os.path.abspath(os.path.join(self.dashboard_dir, 'index.html'))
        try:
            webbrowser.open(f'file://{dashboard_path}')
        except Exception as e:
            logger.error(f"Error opening dashboard in browser: {str(e)}")

        # Start update thread after initial dashboard is generated
        self.update_thread = threading.Thread(target=self._update_loop)
        self.update_thread.daemon = True
        self.update_thread.start()

        logger.info("Dashboard started")

    def stop(self):
        """Stop the dashboard."""
        if not self.is_running:
            logger.warning("Dashboard is not running")
            return

        self.is_running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)

        logger.info("Dashboard stopped")

    def _update_loop(self):
        """Update loop for the dashboard."""
        while self.is_running:
            try:
                self.generate_dashboard()
                time.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"Error updating dashboard: {str(e)}")
                time.sleep(10)  # Wait a bit before retrying

    def generate_dashboard(self):
        """Generate the dashboard HTML."""
        try:
            # Load data
            try:
                self.performance_monitor.load_data()
            except Exception as e:
                logger.error(f"Error loading performance data: {str(e)}")
                # Continue with empty data rather than failing completely

            # Create enhanced dashboard using VisualizationDashboard
            try:
                self._create_enhanced_dashboard()
            except Exception as e:
                logger.error(f"Error creating enhanced dashboard: {str(e)}")
                # Continue without enhanced dashboard

            # For backward compatibility, also generate the old dashboard
            # Generate plots
            try:
                self._generate_plots()
            except Exception as e:
                logger.error(f"Error generating plots: {str(e)}")
                # Continue without plots

            # Generate HTML
            try:
                self._generate_html()
            except Exception as e:
                logger.error(f"Error generating HTML: {str(e)}")
                # This is critical, so we'll re-raise
                raise

            logger.info("Dashboard updated")
        except Exception as e:
            logger.error(f"Error generating dashboard: {str(e)}")
            logger.error(traceback.format_exc())

    def _create_enhanced_dashboard(self):
        """Create enhanced dashboard using VisualizationDashboard."""
        try:
            # Get data from performance monitor
            equity_curve = self.performance_monitor.equity_curve
            trade_history = self.performance_monitor.trade_history
            performance_metrics = self.performance_monitor.get_performance_metrics()

            # Create performance section
            if len(equity_curve) > 0:
                # Extract equity series from DataFrame
                if isinstance(equity_curve, pd.DataFrame) and 'equity' in equity_curve.columns:
                    # Set timestamp as index and extract equity column
                    equity_df = equity_curve.copy()
                    if 'timestamp' in equity_df.columns:
                        equity_df['timestamp'] = pd.to_datetime(equity_df['timestamp'])
                        equity_df = equity_df.set_index('timestamp')
                    equity_series = equity_df['equity']
                else:
                    # Assume it's already a Series
                    equity_series = equity_curve

                self.viz_dashboard.create_performance_section(
                    equity_curve=equity_series,
                    trades=trade_history,
                    metrics=performance_metrics
                )

            # Create data validation section if available
            validation_results = self.performance_monitor.get_validation_results()
            if validation_results:
                self.viz_dashboard.create_data_validation_section(
                    validation_results=validation_results,
                    title="Data Validation"
                )

            # Create model decay section if available
            try:
                model_performance = self.performance_monitor.get_model_performance()

                # Ensure model_performance has a proper DatetimeIndex
                if isinstance(model_performance, pd.DataFrame) and len(model_performance) > 0:
                    # If the index is not a DatetimeIndex, create one
                    if not isinstance(model_performance.index, pd.DatetimeIndex):
                        # Create a synthetic DatetimeIndex
                        model_performance.index = pd.date_range(
                            start=pd.Timestamp.now() - pd.Timedelta(days=len(model_performance)),
                            periods=len(model_performance),
                            freq='D'
                        )

                    self.viz_dashboard.create_model_decay_section(
                        performance_data=model_performance,
                        metric_column='accuracy' if 'accuracy' in model_performance.columns else model_performance.columns[0],
                        title="Model Performance Analysis"
                    )
            except Exception as e:
                logger.error(f"Error creating model decay section: {str(e)}")
                logger.error(f"Error details: {traceback.format_exc()}")
                # Continue with other sections

            # Save the dashboard
            self.viz_dashboard.save_dashboard()

            logger.info("Enhanced dashboard created")
        except Exception as e:
            logger.error(f"Error creating enhanced dashboard: {str(e)}")
            logger.error(f"Error details: {traceback.format_exc()}")

    def _generate_plots(self):
        """Generate plots for the dashboard."""
        # Generate equity curve
        self.performance_monitor.plot_equity_curve(os.path.join(self.dashboard_dir, 'equity_curve.png'))

        # Generate drawdown
        self.performance_monitor.plot_drawdown(os.path.join(self.dashboard_dir, 'drawdown.png'))

        # Generate monthly returns
        self.performance_monitor.plot_monthly_returns(os.path.join(self.dashboard_dir, 'monthly_returns.png'))

        # Generate trade distribution
        self.performance_monitor.plot_trade_distribution(os.path.join(self.dashboard_dir, 'trade_distribution.png'))

        # Generate performance metrics
        self.performance_monitor.plot_performance_metrics(os.path.join(self.dashboard_dir, 'performance_metrics.png'))

        # Generate additional plots
        self._plot_recent_trades(os.path.join(self.dashboard_dir, 'recent_trades.png'))
        self._plot_daily_profit(os.path.join(self.dashboard_dir, 'daily_profit.png'))

    def _plot_recent_trades(self, save_path: str):
        """
        Plot recent trades.

        Args:
            save_path: Path to save the plot
        """
        if len(self.performance_monitor.trade_history) == 0:
            logger.warning("No trade history available for plotting recent trades")
            return

        # Get recent trades (last 20)
        recent_trades = self.performance_monitor.trade_history.sort_values('exit_time', ascending=False).head(20)

        if len(recent_trades) == 0:
            return

        # Create figure
        plt.figure(figsize=(12, 6))

        # Plot recent trades
        colors = ['green' if p > 0 else 'red' for p in recent_trades['profit']]
        plt.bar(range(len(recent_trades)), recent_trades['profit'], color=colors)

        # Add labels and title
        plt.xlabel('Trade')
        plt.ylabel('Profit')
        plt.title('Recent Trades')
        plt.grid(True, axis='y')

        # Add trade details as x-tick labels
        labels = [f"{t['exit_time'].strftime('%m-%d %H:%M')}\n{t['symbol']}" for _, t in recent_trades.iterrows()]
        plt.xticks(range(len(recent_trades)), labels, rotation=45, ha='right')

        plt.tight_layout()

        # Save plot
        plt.savefig(save_path)
        plt.close()

    def _plot_daily_profit(self, save_path: str):
        """
        Plot daily profit.

        Args:
            save_path: Path to save the plot
        """
        if len(self.performance_monitor.trade_history) == 0:
            logger.warning("No trade history available for plotting daily profit")
            return

        # Group trades by day and calculate daily profit
        self.performance_monitor.trade_history['exit_date'] = self.performance_monitor.trade_history['exit_time'].dt.date
        daily_profit = self.performance_monitor.trade_history.groupby('exit_date')['profit'].sum().reset_index()

        if len(daily_profit) == 0:
            return

        # Create figure
        plt.figure(figsize=(12, 6))

        # Plot daily profit
        colors = ['green' if p > 0 else 'red' for p in daily_profit['profit']]
        plt.bar(daily_profit['exit_date'], daily_profit['profit'], color=colors)

        # Add labels and title
        plt.xlabel('Date')
        plt.ylabel('Profit')
        plt.title('Daily Profit')
        plt.grid(True, axis='y')

        # Format x-axis
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.gca().xaxis.set_major_locator(mdates.AutoDateLocator())

        # Rotate date labels
        plt.gcf().autofmt_xdate()

        plt.tight_layout()

        # Save plot
        plt.savefig(save_path)
        plt.close()

    def _generate_html(self):
        """Generate HTML for the dashboard."""
        # Get latest performance metrics
        latest_metrics = None
        if len(self.performance_monitor.performance_data) > 0:
            latest_metrics = self.performance_monitor.performance_data.iloc[-1]

        # Get recent trades
        recent_trades = None
        if len(self.performance_monitor.trade_history) > 0:
            recent_trades = self.performance_monitor.trade_history.sort_values('exit_time', ascending=False).head(10)

        # Generate HTML
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Trading Bot Dashboard</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="refresh" content="{self.update_interval}">
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                }}
                .header {{
                    background-color: #2c3e50;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    margin-bottom: 20px;
                    border-radius: 5px;
                }}
                .metrics-container {{
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                    margin-bottom: 20px;
                }}
                .metric-card {{
                    background-color: white;
                    border-radius: 5px;
                    padding: 15px;
                    margin-bottom: 15px;
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                    width: calc(25% - 20px);
                }}
                .metric-title {{
                    font-size: 14px;
                    color: #7f8c8d;
                    margin-bottom: 5px;
                }}
                .metric-value {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                }}
                .positive {{
                    color: #27ae60;
                }}
                .negative {{
                    color: #e74c3c;
                }}
                .chart-container {{
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }}
                .chart-card {{
                    background-color: white;
                    border-radius: 5px;
                    padding: 15px;
                    margin-bottom: 20px;
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                    width: calc(50% - 20px);
                }}
                .chart-title {{
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 15px;
                    color: #2c3e50;
                }}
                .chart-img {{
                    width: 100%;
                    height: auto;
                }}
                .trades-container {{
                    background-color: white;
                    border-radius: 5px;
                    padding: 15px;
                    margin-bottom: 20px;
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                }}
                .trades-title {{
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 15px;
                    color: #2c3e50;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                }}
                th, td {{
                    padding: 10px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }}
                th {{
                    background-color: #f2f2f2;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 20px;
                    color: #7f8c8d;
                    font-size: 12px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Multi-Terminal Trading Bot Dashboard</h1>
                    <p>Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p>Terminal 1: ARIMA | Terminal 2: LSTM | Terminal 3: TFT | Terminal 4: LSTM+ARIMA | Terminal 5: TFT+ARIMA</p>
                </div>

                <div class="metrics-container">
        """

        # Add metrics
        if latest_metrics is not None:
            metrics = [
                ('Total Trades', f"{latest_metrics['total_trades']:.0f}", None),
                ('Win Rate', f"{latest_metrics['win_rate']:.2%}", 'positive' if latest_metrics['win_rate'] >= 0.5 else 'negative'),
                ('Profit Factor', f"{latest_metrics['profit_factor']:.2f}", 'positive' if latest_metrics['profit_factor'] >= 1.5 else 'negative'),
                ('Total Profit', f"${latest_metrics['total_profit']:.2f}", 'positive' if latest_metrics['total_profit'] > 0 else 'negative'),
                ('Sharpe Ratio', f"{latest_metrics['sharpe_ratio']:.2f}", 'positive' if latest_metrics['sharpe_ratio'] >= 1.0 else 'negative'),
                ('Sortino Ratio', f"{latest_metrics['sortino_ratio']:.2f}", 'positive' if latest_metrics['sortino_ratio'] >= 1.0 else 'negative'),
                ('Max Drawdown', f"{latest_metrics['max_drawdown']:.2%}", 'negative'),
                ('Expectancy', f"{latest_metrics['expectancy']:.2f}", 'positive' if latest_metrics['expectancy'] > 0 else 'negative')
            ]

            for title, value, color_class in metrics:
                class_attr = f'class="metric-value {color_class}"' if color_class else 'class="metric-value"'
                html += f"""
                <div class="metric-card">
                    <div class="metric-title">{title}</div>
                    <div {class_attr}>{value}</div>
                </div>
                """

        html += """
                </div>

                <div class="chart-container">
                    <div class="chart-card">
                        <div class="chart-title">Equity Curve</div>
                        <img class="chart-img" src="equity_curve.png" alt="Equity Curve">
                    </div>
                    <div class="chart-card">
                        <div class="chart-title">Drawdown</div>
                        <img class="chart-img" src="drawdown.png" alt="Drawdown">
                    </div>
                    <div class="chart-card">
                        <div class="chart-title">Monthly Returns</div>
                        <img class="chart-img" src="monthly_returns.png" alt="Monthly Returns">
                    </div>
                    <div class="chart-card">
                        <div class="chart-title">Trade Distribution</div>
                        <img class="chart-img" src="trade_distribution.png" alt="Trade Distribution">
                    </div>
                    <div class="chart-card">
                        <div class="chart-title">Performance Metrics</div>
                        <img class="chart-img" src="performance_metrics.png" alt="Performance Metrics">
                    </div>
                    <div class="chart-card">
                        <div class="chart-title">Daily Profit</div>
                        <img class="chart-img" src="daily_profit.png" alt="Daily Profit">
                    </div>
                </div>

                <div class="trades-container">
                    <div class="trades-title">Recent Trades</div>
        """

        # Add recent trades table
        if recent_trades is not None and len(recent_trades) > 0:
            html += """
                    <table>
                        <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>Type</th>
                                <th>Entry Time</th>
                                <th>Exit Time</th>
                                <th>Entry Price</th>
                                <th>Exit Price</th>
                                <th>Volume</th>
                                <th>Profit</th>
                                <th>Pips</th>
                            </tr>
                        </thead>
                        <tbody>
            """

            for _, trade in recent_trades.iterrows():
                profit_class = 'positive' if trade['profit'] > 0 else 'negative'
                html += f"""
                            <tr>
                                <td>{trade['symbol']}</td>
                                <td>{trade['order_type']}</td>
                                <td>{trade['entry_time'].strftime('%Y-%m-%d %H:%M')}</td>
                                <td>{trade['exit_time'].strftime('%Y-%m-%d %H:%M')}</td>
                                <td>{trade['entry_price']:.5f}</td>
                                <td>{trade['exit_price']:.5f}</td>
                                <td>{trade['volume']:.2f}</td>
                                <td class="{profit_class}">{trade['profit']:.2f}</td>
                                <td>{trade['pips']:.1f}</td>
                            </tr>
                """

            html += """
                        </tbody>
                    </table>
            """
        else:
            html += """
                    <p>No trades available</p>
            """

        html += """
                </div>

                <div class="footer">
                    <p>Multi-Terminal Trading Bot Dashboard - Powered by ARIMA, LSTM, TFT, and Ensemble Models</p>
                </div>
            </div>
        </body>
        </html>
        """

        # Write HTML to file
        with open(os.path.join(self.dashboard_dir, 'index.html'), 'w') as f:
            f.write(html)
