"""
Trading Strategy Module.
This module implements the trading strategy based on multiple model types.
"""
import logging
from typing import Dict, List
import pandas as pd
import numpy as np
from trading.mt5_connector import MT5Connector
from trading.order_manager import OrderManager
from model.model_manager import ModelManager
from data.data_collector import DataCollector
from analysis.feature_engineering import FeatureEngineer
from model.model_evaluator import ModelEvaluator
from config.trading_config import TRADING_CONFIG
from utils.path_utils import get_terminal_model_type, get_model_path

# Configure logger
logger = logging.getLogger('trading_bot.strategy')

class TradingStrategy:
    """
    Trading Strategy class for implementing trading strategies based on LSTM predictions.
    """
    def __init__(
        self,
        mt5_connector: MT5Connector,
        order_manager: OrderManager,
        model_manager: ModelManager,
        data_collector: DataCollector,
        feature_engineer: FeatureEngineer,
        model_evaluator: ModelEvaluator
    ):
        """
        Initialize TradingStrategy.

        Args:
            mt5_connector: MT5Connector instance
            order_manager: OrderManager instance
            model_manager: ModelManager instance
            data_collector: <PERSON>Collector instance
            feature_engineer: FeatureEngineer instance
            model_evaluator: ModelEvaluator instance
        """
        self.mt5 = mt5_connector
        self.order_manager = order_manager
        self.model_manager = model_manager
        self.data_collector = data_collector
        self.feature_engineer = feature_engineer
        self.model_evaluator = model_evaluator

        # Get symbols configuration
        self.symbols = TRADING_CONFIG.get('symbols', {})
        if not self.symbols:
            # Fallback to legacy single symbol configuration
            self.symbols = {TRADING_CONFIG['symbol']: {
                'enabled': True,
                'timeframes': TRADING_CONFIG['timeframes'],
                'lot_size': TRADING_CONFIG.get('lot_size', 0.01)
            }}

        # Get terminal-specific configuration
        self.terminal_id = mt5_connector.active_terminal_id
        self.terminal_config = TRADING_CONFIG.get('terminal_config', {}).get(self.terminal_id, {})

        # Get model type from model_path_utils
        # Terminal 1: ARIMA
        # Terminal 2: LSTM
        # Terminal 3: TFT
        # Terminal 4: LSTM+ARIMA ensemble
        # Terminal 5: TFT+ARIMA ensemble
        self.model_type = get_terminal_model_type(self.terminal_id)
        logger.info(f"Terminal {self.terminal_id} using model type: {self.model_type}")

        # Get model configuration
        self.model_config = TRADING_CONFIG['model']
        self.sequence_length = self.model_config['sequence_length']

        # Get timeframes for each symbol
        self.symbol_timeframes = {}
        for symbol, config in self.symbols.items():
            if config.get('enabled', True):
                self.symbol_timeframes[symbol] = config.get('timeframes', ['M5', 'M15', 'M30', 'H1', 'H4'])

        # Initialize models for each symbol and timeframe
        self.models = {}
        for symbol, timeframes in self.symbol_timeframes.items():
            self.models[symbol] = {}
            for tf in timeframes:
                # Use standardized model naming convention
                model_name = f"{symbol}_{tf}"
                # Get model path using the standardized path utility
                model_path = get_model_path(symbol, tf, model_type=self.model_type, terminal_id=self.terminal_id)

                if not self.model_manager.load_model(model_name, model_path=model_path):
                    logger.warning(f"Model {model_name} not found at {model_path}, will need to be trained")

    def train_models(self, start_date: str, end_date: str = None, symbols: List[str] = None):
        """
        Train models for all symbols and timeframes.

        Args:
            start_date: Start date for training data
            end_date: End date for training data
            symbols: List of symbols to train models for (default: all enabled symbols)
        """
        # Determine which symbols to train
        if symbols is None:
            symbols_to_train = [symbol for symbol, config in self.symbols.items() if config.get('enabled', True)]
        else:
            symbols_to_train = [symbol for symbol in symbols if symbol in self.symbols and self.symbols[symbol].get('enabled', True)]

        if not symbols_to_train:
            logger.error("No valid symbols to train")
            return

        # Train models for each symbol and timeframe
        for symbol in symbols_to_train:
            timeframes = self.symbol_timeframes.get(symbol, ['M5', 'M15', 'M30', 'H1', 'H4'])

            for tf in timeframes:
                logger.info(f"Training {self.model_type.upper()} model for {symbol} ({tf})...")

                # Get data
                df = self.data_collector.get_historical_data(
                    timeframe=tf,
                    start_date=start_date,
                    end_date=end_date,
                    symbol=symbol
                )

                if df is None or len(df) == 0:
                    logger.error(f"Failed to get data for {symbol} ({tf})")
                    continue

                # Create features
                df_features = self.feature_engineer.create_features(df)

                # Normalize features
                df_norm = self.feature_engineer.normalize_features(df_features)

                # Prepare data for training
                X_train, y_train, X_val, y_val, X_test, y_test = self.feature_engineer.prepare_data_for_training(
                    df=df_norm,
                    sequence_length=self.sequence_length,
                    target_column='returns',
                    train_test_split=self.model_config['train_test_split'],
                    validation_split=self.model_config['validation_split']
                )

                # Check if we have enough data
                if len(X_train) == 0:
                    logger.error(f"Not enough data for {symbol} ({tf})")
                    logger.info(f"Skipping training for {symbol} ({tf})")
                    continue

                # Create model based on terminal configuration
                # Use standardized model naming convention
                model_name = f"{symbol}_{tf}"
                input_size = X_train.shape[2]  # Number of features

                # Get model-specific parameters based on terminal type
                if self.model_type == 'lstm':
                    # Terminal 2: LSTM
                    self.model_manager.create_model(
                        model_name=model_name,
                        model_type='lstm',
                        input_size=input_size,
                        hidden_size=self.model_config.get('lstm', {}).get('hidden_size', 64),
                        num_layers=self.model_config.get('lstm', {}).get('num_layers', 2),
                        output_size=1,
                        terminal_id=self.terminal_id  # Pass terminal_id for proper path construction
                    )
                elif self.model_type == 'tft':
                    # Terminal 3: TFT
                    self.model_manager.create_model(
                        model_name=model_name,
                        model_type='tft',
                        input_size=input_size,
                        hidden_size=self.model_config.get('tft', {}).get('hidden_size', 64),
                        num_layers=self.model_config.get('tft', {}).get('lstm_layers', 2),
                        output_size=1,
                        terminal_id=self.terminal_id  # Pass terminal_id for proper path construction
                    )
                elif self.model_type == 'arima':
                    # Terminal 1: ARIMA
                    self.model_manager.create_model(
                        model_name=model_name,
                        model_type='arima',
                        terminal_id=self.terminal_id  # Pass terminal_id for proper path construction
                    )
                elif self.model_type in ['lstm_arima', 'tft_arima']:
                    # Terminal 4: LSTM+ARIMA or Terminal 5: TFT+ARIMA
                    # For ensemble models, we need to train the component models first
                    # Determine component models based on ensemble type
                    if self.model_type == 'lstm_arima':
                        component_models = ['lstm', 'arima']
                        default_weights = [0.6, 0.4]
                    else:  # tft_arima
                        component_models = ['tft', 'arima']
                        default_weights = [0.7, 0.3]

                    # Get ensemble configuration from terminal_config if available
                    ensemble_config = self.terminal_config.get('ensemble_config', {})

                    # Get component models and weights from ensemble_config if available
                    component_models = ensemble_config.get('models', component_models)
                    weights = ensemble_config.get('weights', default_weights)

                    # Create and train component models
                    component_model_names = []
                    for component_type in component_models:
                        component_name = f"{symbol}_{tf}"
                        component_model_names.append(component_name)

                        # Create component model with appropriate parameters
                        if component_type == 'lstm':
                            self.model_manager.create_model(
                                model_name=component_name,
                                model_type='lstm',
                                input_size=input_size,
                                hidden_size=self.model_config.get('lstm', {}).get('hidden_size', 64),
                                num_layers=self.model_config.get('lstm', {}).get('num_layers', 2),
                                output_size=1
                            )
                        elif component_type == 'tft':
                            self.model_manager.create_model(
                                model_name=component_name,
                                model_type='tft',
                                input_size=input_size,
                                hidden_size=self.model_config.get('tft', {}).get('hidden_size', 64),
                                num_layers=self.model_config.get('tft', {}).get('lstm_layers', 2),
                                output_size=1
                            )
                        elif component_type == 'arima':
                            self.model_manager.create_model(
                                model_name=component_name,
                                model_type='arima'
                            )

                        # Train component model
                        self._train_specific_model(
                            model_name=component_name,
                            model_type=component_type,
                            X_train=X_train,
                            y_train=y_train,
                            X_val=X_val,
                            y_val=y_val,
                            X_test=X_test,
                            y_test=y_test
                        )

                    # Create ensemble model
                    from model.ensemble_model import EnsembleModel
                    ensemble = EnsembleModel(
                        model_manager=self.model_manager,
                        ensemble_name=model_name,
                        model_names=component_model_names,
                        weights=weights
                    )

                    # Store ensemble in model manager
                    self.model_manager.models[model_name] = ensemble
                    self.model_manager.models[f"{model_name}_type"] = 'ensemble'

                    # Skip the regular training for ensemble models
                    logger.info(f"Ensemble model {model_name} created with components: {component_model_names}")

                    # Store model reference
                    if symbol not in self.models:
                        self.models[symbol] = {}
                    self.models[symbol][tf] = model_name

                    # Continue to next timeframe
                    continue

                # Train model
                self._train_specific_model(
                    model_name=model_name,
                    model_type=self.model_type,
                    X_train=X_train,
                    y_train=y_train,
                    X_val=X_val,
                    y_val=y_val,
                    X_test=X_test,
                    y_test=y_test
                )

                # Store model reference
                if symbol not in self.models:
                    self.models[symbol] = {}
                self.models[symbol][tf] = model_name

    def _train_specific_model(self, model_name, model_type, X_train, y_train, X_val, y_val, X_test, y_test):
        """
        Train a specific model type.

        Args:
            model_name: Name of the model
            model_type: Type of model ('lstm', 'tft', 'arima')
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets
            X_test: Test features
            y_test: Test targets
        """
        # Get feature names for TFT models
        feature_names = None
        if model_type == 'tft':
            # Create dummy feature names if not available
            feature_names = [f"feature_{i}" for i in range(X_train.shape[2])]

        # Train model
        success = self.model_manager.train_model(
            model_name=model_name,
            X_train=X_train,
            y_train=y_train,
            X_val=X_val,
            y_val=y_val,
            feature_names=feature_names
        )

        if success:
            # Extract symbol and timeframe from model_name (assuming format: symbol_timeframe)
            parts = model_name.split('_')
            if len(parts) >= 2:
                symbol = parts[0]
                timeframe = parts[1]

                # Get standardized model path
                model_path = get_model_path(symbol, timeframe, model_type=model_type, terminal_id=self.terminal_id)

                # Save model with standardized path
                self.model_manager.save_model(model_name, model_path=model_path, terminal_id=self.terminal_id)
                logger.info(f"Model {model_name} saved to {model_path}")
            else:
                logger.warning(f"Could not extract symbol and timeframe from model_name: {model_name}")
        else:
            logger.error(f"Failed to train model {model_name}")

        # Evaluate model
        metrics = self.model_manager.evaluate_model(
            model_name=model_name,
            X=X_test,
            y=y_test
        )

        logger.info(f"Model {model_name} trained and evaluated:")
        for metric, value in metrics.items():
            logger.info(f"  {metric}: {value:.6f}")

    def update_models(self, lookback_days: int = 30, symbols: List[str] = None):
        """
        Update models with recent data.

        Args:
            lookback_days: Number of days to look back for updating
            symbols: List of symbols to update models for (default: all enabled symbols)
        """
        # Determine which symbols to update
        if symbols is None:
            symbols_to_update = [symbol for symbol, config in self.symbols.items() if config.get('enabled', True)]
        else:
            symbols_to_update = [symbol for symbol in symbols if symbol in self.symbols and self.symbols[symbol].get('enabled', True)]

        if not symbols_to_update:
            logger.error("No valid symbols to update")
            return

        # Update models for each symbol and timeframe
        for symbol in symbols_to_update:
            timeframes = self.symbol_timeframes.get(symbol, ['M5', 'M15', 'M30', 'H1', 'H4'])

            for tf in timeframes:
                # Use standardized model naming convention
                model_name = f"{symbol}_{tf}"

                if model_name not in self.model_manager.list_models():
                    logger.warning(f"Model {model_name} not found, skipping update")
                    continue

                logger.info(f"Updating model {model_name}...")

                # Get recent data
                df = self.data_collector.get_latest_data(
                    timeframe=tf,
                    num_bars=self._calculate_bars_for_timeframe(tf, lookback_days),
                    symbol=symbol
                )

                if df is None or len(df) == 0:
                    logger.error(f"Failed to get data for {symbol} ({tf})")
                    continue

                # Create features
                df_features = self.feature_engineer.create_features(df)

                # Normalize features
                df_norm = self.feature_engineer.normalize_features(df_features)

                # Get model type
                model_type = self.model_manager.models.get(f"{model_name}_type", self.model_type)

                if model_type in ['lstm', 'tft']:
                    # Create sequences for sequence models
                    X, y = self.feature_engineer.create_sequences(
                        df=df_norm,
                        sequence_length=self.sequence_length,
                        target_column='returns'
                    )

                    # Update model
                    self.model_manager.update_model(
                        model_name=model_name,
                        X=X,
                        y=y
                    )

                    logger.info(f"Model {model_name} updated with {len(X)} samples")

                elif model_type in ['lstm_arima', 'tft_arima']:
                    # For ensemble models, we need to update the component models
                    # Determine component models based on ensemble type
                    if model_type == 'lstm_arima':
                        component_models = ['lstm', 'arima']
                        default_weights = [0.6, 0.4]
                    else:  # tft_arima
                        component_models = ['tft', 'arima']
                        default_weights = [0.7, 0.3]

                    # Get ensemble configuration from terminal_config if available
                    ensemble_config = self.terminal_config.get('ensemble_config', {})

                    # Get component models and weights from ensemble_config if available
                    component_models = ensemble_config.get('models', component_models)
                    weights = ensemble_config.get('weights', default_weights)

                    # Update each component model
                    for component_type in component_models:
                        # Use standardized model naming convention
                        component_name = f"{symbol}_{tf}"

                        if component_type in ['lstm', 'tft']:
                            # Create sequences for sequence models
                            X, y = self.feature_engineer.create_sequences(
                                df=df_norm,
                                sequence_length=self.sequence_length,
                                target_column='returns'
                            )

                            # Update model
                            self.model_manager.update_model(
                                model_name=component_name,
                                X=X,
                                y=y
                            )

                            logger.info(f"Component model {component_name} updated with {len(X)} samples")

                        elif component_type == 'arima':
                            # For ARIMA models, we need the raw time series
                            y = df_norm['returns'].values

                            # Update model
                            self.model_manager.update_model(
                                model_name=component_name,
                                X=None,
                                y=y
                            )

                            logger.info(f"ARIMA component model {component_name} updated with {len(y)} samples")

                    # Update ensemble model weights if needed
                    ensemble_model = self.model_manager.models.get(model_name)
                    if ensemble_model and hasattr(ensemble_model, 'set_weights'):
                        ensemble_model.set_weights(weights)
                        logger.info(f"Updated ensemble model {model_name} weights: {weights}")

                    logger.info(f"Ensemble model {model_name} updated")

                elif model_type == 'arima':
                    # For ARIMA models, we need the raw time series
                    y = df_norm['returns'].values

                    # Update model
                    self.model_manager.update_model(
                        model_name=model_name,
                        X=None,
                        y=y
                    )

                    logger.info(f"ARIMA model {model_name} updated with {len(y)} samples")

    def _calculate_bars_for_timeframe(self, timeframe: str, lookback_days: int) -> int:
        """
        Calculate the number of bars needed for a given timeframe and lookback period.

        Args:
            timeframe: Timeframe (e.g., 'M5', 'M15', 'M30', 'H1', 'H4')
            lookback_days: Number of days to look back

        Returns:
            int: Number of bars
        """
        # Extract the numeric part of the timeframe
        if timeframe.startswith('M'):
            # Minutes timeframe
            minutes = int(timeframe[1:])
            return lookback_days * 24 * 60 // minutes
        elif timeframe.startswith('H'):
            # Hours timeframe
            hours = int(timeframe[1:])
            return lookback_days * 24 // hours
        elif timeframe.startswith('D'):
            # Days timeframe
            return lookback_days
        else:
            # Default to daily bars
            logger.warning(f"Unknown timeframe format: {timeframe}, using daily bars")
            return lookback_days

    def generate_signals(self, symbols: List[str] = None) -> Dict[str, Dict[str, float]]:
        """
        Generate trading signals for all symbols and timeframes.

        Args:
            symbols: List of symbols to generate signals for (default: all enabled symbols)

        Returns:
            Dict[str, Dict[str, float]]: Dictionary of signals for each symbol and timeframe
        """
        # Determine which symbols to generate signals for
        if symbols is None:
            symbols_to_process = [symbol for symbol, config in self.symbols.items() if config.get('enabled', True)]
        else:
            symbols_to_process = [symbol for symbol in symbols if symbol in self.symbols and self.symbols[symbol].get('enabled', True)]

        if not symbols_to_process:
            logger.error("No valid symbols to generate signals for")
            return {}

        # Initialize signals dictionary
        all_signals = {}

        # Generate signals for each symbol
        for symbol in symbols_to_process:
            timeframes = self.symbol_timeframes.get(symbol, ['M5', 'M15', 'M30', 'H1', 'H4'])
            signals = {}

            for tf in timeframes:
                try:
                    # Use standardized model naming convention
                    model_name = f"{symbol}_{tf}"

                    if model_name not in self.model_manager.list_models():
                        logger.warning(f"Model {model_name} not found, skipping signal generation")
                        continue

                    # Check if MT5 is connected
                    if not self.mt5.check_connection():
                        logger.error(f"MT5 is not connected. Cannot generate signals for {symbol} ({tf})")
                        continue

                    # Get latest data
                    df = self.data_collector.get_latest_data(
                        timeframe=tf,
                        num_bars=self.sequence_length + 100,  # Get extra bars for feature engineering
                        symbol=symbol
                    )

                    if df is None or len(df) == 0:
                        logger.error(f"Failed to get data for {symbol} ({tf})")
                        continue

                    # Check if we have enough data
                    if len(df) < self.sequence_length:
                        logger.error(f"Not enough data for {symbol} ({tf}). Need at least {self.sequence_length} bars, got {len(df)}")
                        continue

                    # Create features
                    df_features = self.feature_engineer.create_features(df)

                    # Normalize features
                    df_norm = self.feature_engineer.normalize_features(df_features)

                    # Get model type
                    model_type = self.model_manager.models.get(f"{model_name}_type", self.model_type)

                    # Generate prediction based on model type
                    # Get feature columns and prepare data
                    feature_columns = [col for col in df_norm.columns if col != 'returns']
                    latest_sequence = df_norm[feature_columns].iloc[-self.sequence_length:].values
                    latest_sequence = np.expand_dims(latest_sequence, axis=0)  # Add batch dimension
                    time_series = df_norm['returns'].values

                    # Create feature names for TFT models
                    feature_names = feature_columns if model_type == 'tft' else None

                    if model_type == 'lstm':
                        # For LSTM models, we use the predict method with sequence data
                        prediction = self.model_manager.predict(
                            model_name=model_name,
                            X=latest_sequence
                        )

                    elif model_type == 'tft':
                        # For TFT models, we use the predict method with sequence data and feature names
                        prediction = self.model_manager.predict(
                            model_name=model_name,
                            X=latest_sequence,
                            feature_names=feature_names
                        )

                    elif model_type in ['lstm_arima', 'tft_arima']:
                        # For ensemble models, we need to provide both sequence data and time series
                        prediction = self.model_manager.predict(
                            model_name=model_name,
                            X=latest_sequence,
                            feature_names=feature_names,
                            time_series=time_series
                        )

                    elif model_type == 'arima':
                        # For ARIMA models, we need the raw time series data
                        prediction = self.model_manager.predict(
                            model_name=model_name,
                            X=time_series,  # Pass the time series directly
                            steps=1
                        )
                    else:
                        logger.error(f"Unsupported model type: {model_type}")
                        continue

                    if prediction is None:
                        logger.error(f"Failed to generate prediction for {symbol} ({tf})")
                        continue

                    # Store signal
                    signal_value = float(prediction[0][0]) if len(prediction.shape) > 1 else float(prediction[0])
                    signals[tf] = signal_value

                    logger.info(f"Signal for {symbol} ({tf}): {signal_value:.6f}")

                except Exception as e:
                    logger.error(f"Error generating signal for {symbol} ({tf}): {str(e)}")
                    import traceback
                    logger.error(traceback.format_exc())
                    continue

            # Store signals for this symbol
            if signals:
                all_signals[symbol] = signals

        return all_signals

    def combine_signals(self, signals: Dict[str, float], symbol: str = None) -> float:
        """
        Combine signals from multiple timeframes based on terminal configuration.

        Args:
            signals: Dictionary of signals for each timeframe
            symbol: Symbol for which to combine signals (for logging purposes)

        Returns:
            float: Combined signal
        """
        if not signals:
            logger.warning("No signals to combine")
            return 0.0

        # Use terminal-specific timeframe weights if available
        timeframe_weights = self.terminal_config.get('timeframe_weights', None)

        if timeframe_weights:
            # Use terminal-specific weights
            weighted_sum = 0.0
            total_weight = 0.0

            for tf, signal in signals.items():
                if tf in timeframe_weights:
                    weight = timeframe_weights[tf]
                    weighted_sum += signal * weight
                    total_weight += weight

            # Normalize by total weight used
            if total_weight > 0:
                combined_signal = weighted_sum / total_weight
            else:
                # Fallback to simple average if no weights were applied
                combined_signal = sum(signals.values()) / len(signals)
                logger.warning("No valid timeframe weights found, using simple average")
        else:
            # Use default weights with higher weight for M5
            default_weights = {
                'M5': 0.5,   # 50% weight for M5
                'M15': 0.2,  # 20% weight for M15
                'M30': 0.15, # 15% weight for M30
                'H1': 0.1,   # 10% weight for H1
                'H4': 0.05   # 5% weight for H4
            }

            # Calculate weighted average
            weighted_sum = 0.0
            total_weight = 0.0

            for tf, signal in signals.items():
                if tf in default_weights:
                    weighted_sum += signal * default_weights[tf]
                    total_weight += default_weights[tf]

            # Normalize by total weight used
            if total_weight > 0:
                combined_signal = weighted_sum / total_weight
            else:
                # Fallback to simple average if no weights were applied
                combined_signal = sum(signals.values()) / len(signals)
                logger.warning("No valid timeframe weights found, using simple average")

        symbol_str = f" for {symbol}" if symbol else ""
        logger.info(f"Combined signal{symbol_str}: {combined_signal:.6f}")
        return combined_signal

    def execute_trades(self, symbol: str, signal: float, atr_value: float = None, signals: Dict[str, float] = None):
        """
        Execute trades based on the signal for a specific symbol.

        Args:
            symbol: Symbol to trade
            signal: Combined trading signal
            atr_value: ATR value for dynamic SL/TP calculation
            signals: Dictionary of signals for each timeframe
        """
        # Default signals if not provided
        signals = signals or {}

        # Get symbol configuration
        symbol_config = self.symbols.get(symbol, {})
        if not symbol_config.get('enabled', True):
            logger.info(f"Symbol {symbol} is disabled for trading")
            return

        # Get terminal-specific signal thresholds
        signal_threshold = self.terminal_config.get('signal_threshold', 0.3)
        signal_reversal = self.terminal_config.get('signal_reversal', 0.7)

        # Get lot size from symbol configuration
        lot_size = symbol_config.get('lot_size', TRADING_CONFIG.get('lot_size', 0.01))

        # Get M5 signal if available (for confirmation)
        m5_signal = signals.get('M5', 0.0)
        h4_signal = signals.get('H4', 0.0)  # H4 signal for trend confirmation

        # Check if signal is strong enough to trade
        if abs(signal) < signal_threshold:
            logger.info(f"Combined signal ({signal:.6f}) for {symbol} not strong enough to trade")
            return

        # Get current positions for this symbol
        position_status = self.order_manager.get_position_status(symbol=symbol)

        # Get maximum positions allowed for this symbol
        max_positions = symbol_config.get('max_positions', TRADING_CONFIG['risk_management'].get('max_open_positions', 1))

        # Check if we already have the maximum number of positions for this symbol
        if position_status['has_position'] and len(position_status['positions']) >= max_positions:
            logger.info(f"Already have maximum {max_positions} positions for {symbol}, not opening new positions")
            # We can still close positions based on signals

        # Determine action based on signal and current positions
        if signal > 0.5 and (not position_status['has_position'] or len(position_status['positions']) < max_positions):
            # Check if M5 signal confirms the direction (if available)
            if m5_signal > 0.3 or 'M5' not in signals:
                # Additional confirmation from H4 for trend direction (if available)
                trend_confirmed = True
                if 'H4' in signals and abs(h4_signal) > 0.3:
                    trend_confirmed = h4_signal > 0
                    if not trend_confirmed:
                        logger.info(f"H4 signal ({h4_signal:.6f}) does not confirm long trend for {symbol}, not trading")

                if trend_confirmed:
                    # Open long position
                    logger.info(f"Opening LONG position for {symbol}")
                    logger.info(f"M5 signal: {m5_signal:.6f}, H4 signal: {h4_signal:.6f}, Combined signal: {signal:.6f}")

                    # Get SL/TP configuration for this symbol
                    sl_tp_config = symbol_config.get('sl_tp', TRADING_CONFIG['sl_tp'])

                    self.order_manager.place_market_order(
                        symbol=symbol,
                        order_type='BUY',
                        volume=lot_size,
                        atr_value=atr_value,
                        sl_tp_config=sl_tp_config,
                        comment=f"{self.model_type.upper()} Signal: {signal:.4f} (M5: {m5_signal:.4f})"
                    )
            else:
                logger.info(f"M5 signal ({m5_signal:.6f}) does not confirm long direction for {symbol}, not trading")

        elif signal < -0.5 and (not position_status['has_position'] or len(position_status['positions']) < max_positions):
            # Check if M5 signal confirms the direction (if available)
            if m5_signal < -0.3 or 'M5' not in signals:
                # Additional confirmation from H4 for trend direction (if available)
                trend_confirmed = True
                if 'H4' in signals and abs(h4_signal) > 0.3:
                    trend_confirmed = h4_signal < 0
                    if not trend_confirmed:
                        logger.info(f"H4 signal ({h4_signal:.6f}) does not confirm short trend for {symbol}, not trading")

                if trend_confirmed:
                    # Open short position
                    logger.info(f"Opening SHORT position for {symbol}")
                    logger.info(f"M5 signal: {m5_signal:.6f}, H4 signal: {h4_signal:.6f}, Combined signal: {signal:.6f}")

                    # Get SL/TP configuration for this symbol
                    sl_tp_config = symbol_config.get('sl_tp', TRADING_CONFIG['sl_tp'])

                    self.order_manager.place_market_order(
                        symbol=symbol,
                        order_type='SELL',
                        volume=lot_size,
                        atr_value=atr_value,
                        sl_tp_config=sl_tp_config,
                        comment=f"{self.model_type.upper()} Signal: {signal:.4f} (M5: {m5_signal:.4f})"
                    )
            else:
                logger.info(f"M5 signal ({m5_signal:.6f}) does not confirm short direction for {symbol}, not trading")

        elif abs(signal) < signal_threshold and position_status['has_position']:
            # Close positions if signal is weak
            logger.info(f"Closing positions for {symbol} due to weak signal")
            self.order_manager.close_all_positions(symbol=symbol)

        # Additional logic for signal reversals
        elif position_status['has_position']:
            # Check for strong reversal signals
            for position in position_status['positions']:
                position_type = position['type']

                # Check for strong reversal signals based on terminal configuration
                if position_type == 'BUY' and signal < -signal_reversal:
                    logger.info(f"Closing LONG position {position['ticket']} for {symbol} due to strong reversal signal: {signal:.6f}")
                    self.order_manager.close_position(position['ticket'])
                elif position_type == 'SELL' and signal > signal_reversal:
                    logger.info(f"Closing SHORT position {position['ticket']} for {symbol} due to strong reversal signal: {signal:.6f}")
                    self.order_manager.close_position(position['ticket'])

                # Additional check for M5 reversals if available
                elif position_type == 'BUY' and m5_signal < -signal_reversal:
                    logger.info(f"Closing LONG position {position['ticket']} for {symbol} due to strong M5 reversal: {m5_signal:.6f}")
                    self.order_manager.close_position(position['ticket'])
                elif position_type == 'SELL' and m5_signal > signal_reversal:
                    logger.info(f"Closing SHORT position {position['ticket']} for {symbol} due to strong M5 reversal: {m5_signal:.6f}")
                    self.order_manager.close_position(position['ticket'])

    def apply_trailing_stop(self, trail_points: int = 100, symbols: List[str] = None):
        """
        Apply trailing stop to open positions.

        Args:
            trail_points: Trailing stop distance in points
            symbols: List of symbols to apply trailing stop for (default: all symbols)
        """
        # Determine which symbols to process
        if symbols is None:
            symbols_to_process = [symbol for symbol, config in self.symbols.items() if config.get('enabled', True)]
        else:
            symbols_to_process = [symbol for symbol in symbols if symbol in self.symbols and self.symbols[symbol].get('enabled', True)]

        if not symbols_to_process:
            logger.warning("No valid symbols to apply trailing stop")
            return

        # Process each symbol
        for symbol in symbols_to_process:
            # Get current positions for this symbol
            position_status = self.order_manager.get_position_status()

            if not position_status['has_position']:
                continue

            # Filter positions for this specific symbol
            symbol_positions = [pos for pos in position_status['positions'] if pos.get('symbol') == symbol]

            if not symbol_positions:
                continue

            # Get symbol-specific trailing stop configuration
            symbol_config = self.symbols.get(symbol, {})
            symbol_trail_points = symbol_config.get('trail_points', trail_points)

            # Apply trailing stop to each position for this symbol
            for position in symbol_positions:
                self.order_manager.trailing_stop(position['ticket'], symbol_trail_points)
                logger.debug(f"Applied trailing stop of {symbol_trail_points} points to position {position['ticket']} for {symbol}")

    def run_trading_cycle(self, symbols: List[str] = None):
        """
        Run a complete trading cycle for multiple symbols.

        Args:
            symbols: List of symbols to run trading cycle for (default: all enabled symbols)
        """
        try:
            # Determine which symbols to process
            if symbols is None:
                symbols_to_process = [symbol for symbol, config in self.symbols.items() if config.get('enabled', True)]
            else:
                symbols_to_process = [symbol for symbol in symbols if symbol in self.symbols and self.symbols[symbol].get('enabled', True)]

            if not symbols_to_process:
                logger.error("No valid symbols to process")
                return

            logger.info(f"Running trading cycle for {len(symbols_to_process)} symbols: {', '.join(symbols_to_process)}")

            # Check if models need to be updated
            self.update_models(symbols=symbols_to_process)

            # Generate signals for all symbols
            all_signals = self.generate_signals(symbols=symbols_to_process)

            if not all_signals:
                logger.warning("No signals generated for any symbol")
                return

            # Process each symbol
            for symbol, signals in all_signals.items():
                logger.info(f"Processing signals for {symbol}...")

                if not signals:
                    logger.warning(f"No signals generated for {symbol}")
                    continue

                # Combine signals for this symbol
                combined_signal = self.combine_signals(signals, symbol)

                # Get ATR value for dynamic SL/TP
                atr_value = None
                try:
                    df = self.data_collector.get_latest_data(
                        timeframe='M30',
                        num_bars=50,  # Get more bars to ensure ATR can be calculated
                        symbol=symbol
                    )
                    df_features = self.feature_engineer.create_features(df)
                    atr_column = 'atr14' if 'atr14' in df_features.columns else 'atr_14'

                    if atr_column in df_features.columns and len(df_features) > 0:
                        atr_value = df_features[atr_column].iloc[-1]
                    else:
                        logger.warning(f"ATR not available in features for {symbol}, using default value")
                        atr_value = 100.0  # Default value
                except Exception as e:
                    logger.error(f"Failed to get ATR value for {symbol}: {str(e)}")
                    atr_value = 100.0  # Default value

                # Execute trades for this symbol
                self.execute_trades(symbol, combined_signal, atr_value, signals=signals)

            # Apply trailing stop to all positions
            trail_points = self.terminal_config.get('trail_points', TRADING_CONFIG['sl_tp'].get('trail_points', 100))
            self.apply_trailing_stop(trail_points=trail_points)

            # Check for model decay
            for symbol in symbols_to_process:
                try:
                    decay_detected, reason = self.model_evaluator.detect_model_decay(symbol=symbol)
                    if decay_detected:
                        logger.warning(f"Model decay detected for {symbol}: {reason}")
                        # Retrain models if decay is detected
                        self.train_models(
                            start_date=(pd.Timestamp.now() - pd.Timedelta(days=365)).strftime('%Y-%m-%d'),
                            symbols=[symbol]
                        )
                except Exception as e:
                    logger.error(f"Error checking model decay for {symbol}: {str(e)}")

            # Log daily performance
            daily_performance = self.order_manager.get_daily_performance()
            logger.info(f"Daily performance: {daily_performance}")

        except Exception as e:
            logger.error(f"Error in trading cycle: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    def backtest(self, start_date: str, end_date: str = None):
        """
        Run backtest on historical data.

        Args:
            start_date: Start date for backtest
            end_date: End date for backtest

        Returns:
            pd.DataFrame: Backtest results
        """
        logger.info(f"Starting backtest from {start_date} to {end_date or 'now'}")

        # Get data for all timeframes
        data = {}
        for tf in self.timeframes:
            df = self.data_collector.get_historical_data(
                timeframe=tf,
                start_date=start_date,
                end_date=end_date
            )

            if df is None or len(df) == 0:
                logger.error(f"Failed to get data for {self.symbol} ({tf})")
                continue

            # Create features
            df_features = self.feature_engineer.create_features(df)

            # Normalize features
            df_norm = self.feature_engineer.normalize_features(df_features)

            data[tf] = df_norm

        # Check if we have data for all timeframes
        if len(data) != len(self.timeframes):
            logger.error("Missing data for some timeframes")
            return None

        # Get the shortest timeframe data for the main backtest
        main_tf = min(self.timeframes, key=lambda x: int(x[1:]))
        main_data = data[main_tf]

        # Initialize backtest results
        results = pd.DataFrame(index=main_data.index)
        results['close'] = main_data['close']
        results['signal'] = 0.0
        results['m5_signal'] = 0.0
        results['combined_signal'] = 0.0
        results['position'] = 0
        results['trade'] = 0
        results['profit'] = 0.0
        results['equity'] = 1.0  # Start with 1.0 (100%)

        # Train models on the first part of the data
        train_size = int(len(main_data) * 0.6)
        for tf in self.timeframes:
            model_name = f"{self.symbol}_{tf}_backtest"

            # Prepare training data
            train_data = data[tf].iloc[:train_size]
            X_train, y_train = self.feature_engineer.create_sequences(
                df=train_data,
                sequence_length=self.sequence_length,
                target_column='returns'
            )

            # Split into train and validation
            val_size = int(len(X_train) * 0.2)
            X_val, y_val = X_train[-val_size:], y_train[-val_size:]
            X_train, y_train = X_train[:-val_size], y_train[:-val_size]

            # Create and train model
            input_size = X_train.shape[2]
            self.model_manager.create_model(
                model_name=model_name,
                input_size=input_size,
                hidden_size=self.model_config['hidden_size'],
                num_layers=self.model_config['num_layers'],
                output_size=1
            )

            self.model_manager.train_model(
                model_name=model_name,
                X_train=X_train,
                y_train=y_train,
                X_val=X_val,
                y_val=y_val
            )

        # Run backtest
        position = 0
        entry_price = 0.0

        for i in range(train_size + self.sequence_length, len(main_data)):
            # Generate signals for each timeframe
            signals = {}

            for tf in self.timeframes:
                model_name = f"{self.symbol}_{tf}_backtest"

                # Get sequence
                feature_columns = [col for col in data[tf].columns if col != 'returns']
                sequence = data[tf][feature_columns].iloc[i-self.sequence_length:i].values
                sequence = np.expand_dims(sequence, axis=0)  # Add batch dimension

                # Generate prediction
                prediction = self.model_manager.predict(
                    model_name=model_name,
                    X=sequence
                )

                signals[tf] = float(prediction[0][0])

            # Combine signals
            combined_signal = self.combine_signals(signals)

            # Store signals in results
            results.loc[main_data.index[i], 'signal'] = combined_signal
            # M5 signal is already stored in the results DataFrame in the execute_trades logic

            # Execute trading logic
            current_price = main_data['close'].iloc[i]

            # Get M5 signal if available
            m5_signal = signals.get('M5', 0.0)

            # Store signals in results
            results.loc[main_data.index[i], 'combined_signal'] = combined_signal
            results.loc[main_data.index[i], 'm5_signal'] = m5_signal

            if position == 0:  # No position
                if combined_signal > 0.5:
                    # Check if M5 signal confirms the direction
                    if m5_signal > 0.3 or 'M5' not in signals:
                        # Open long position
                        position = 1
                        entry_price = current_price
                        results.loc[main_data.index[i], 'position'] = position
                        results.loc[main_data.index[i], 'trade'] = 1
                        logger.info(f"Backtest: Opening LONG position at {entry_price} on {main_data.index[i]}")
                        logger.info(f"Backtest: M5 signal: {m5_signal:.6f}, Combined signal: {combined_signal:.6f}")
                elif combined_signal < -0.5:
                    # Check if M5 signal confirms the direction
                    if m5_signal < -0.3 or 'M5' not in signals:
                        # Open short position
                        position = -1
                        entry_price = current_price
                        results.loc[main_data.index[i], 'position'] = position
                        results.loc[main_data.index[i], 'trade'] = -1
                        logger.info(f"Backtest: Opening SHORT position at {entry_price} on {main_data.index[i]}")
                        logger.info(f"Backtest: M5 signal: {m5_signal:.6f}, Combined signal: {combined_signal:.6f}")

            elif position == 1:  # Long position
                # Check for exit conditions
                if combined_signal < 0.3 or m5_signal < -0.7:
                    # Close long position
                    profit = (current_price - entry_price) / entry_price
                    results.loc[main_data.index[i], 'profit'] = profit
                    results.loc[main_data.index[i], 'trade'] = -1
                    position = 0
                    results.loc[main_data.index[i], 'position'] = position

                    # Log exit reason
                    if m5_signal < -0.7:
                        logger.info(f"Backtest: Closing LONG position due to M5 reversal at {current_price} on {main_data.index[i]}, profit: {profit:.4f}")
                    else:
                        logger.info(f"Backtest: Closing LONG position due to weak signal at {current_price} on {main_data.index[i]}, profit: {profit:.4f}")

            elif position == -1:  # Short position
                # Check for exit conditions
                if combined_signal > -0.3 or m5_signal > 0.7:
                    # Close short position
                    profit = (entry_price - current_price) / entry_price
                    results.loc[main_data.index[i], 'profit'] = profit
                    results.loc[main_data.index[i], 'trade'] = 1
                    position = 0
                    results.loc[main_data.index[i], 'position'] = position

                    # Log exit reason
                    if m5_signal > 0.7:
                        logger.info(f"Backtest: Closing SHORT position due to M5 reversal at {current_price} on {main_data.index[i]}, profit: {profit:.4f}")
                    else:
                        logger.info(f"Backtest: Closing SHORT position due to weak signal at {current_price} on {main_data.index[i]}, profit: {profit:.4f}")

            # Update equity
            if i > 0:
                results.loc[main_data.index[i], 'equity'] = results.loc[main_data.index[i-1], 'equity'] * (1 + results.loc[main_data.index[i], 'profit'])

        # Calculate performance metrics
        total_trades = results['trade'].abs().sum() / 2  # Divide by 2 because each trade has entry and exit
        winning_trades = results[results['profit'] > 0]['profit'].count()
        losing_trades = results[results['profit'] < 0]['profit'].count()
        win_rate = winning_trades / total_trades if total_trades > 0 else 0.0

        avg_win = results[results['profit'] > 0]['profit'].mean() if winning_trades > 0 else 0.0
        avg_loss = results[results['profit'] < 0]['profit'].mean() if losing_trades > 0 else 0.0
        profit_factor = (avg_win * winning_trades) / abs(avg_loss * losing_trades) if losing_trades > 0 and avg_loss != 0 else float('inf')

        total_return = results['equity'].iloc[-1] - 1.0

        # Calculate drawdown
        peak = results['equity'].expanding().max()
        drawdown = (results['equity'] / peak - 1.0)
        max_drawdown = drawdown.min()

        # Calculate Sharpe ratio
        returns = results['equity'].pct_change().dropna()
        sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0.0

        # Calculate Sortino ratio
        downside_returns = returns[returns < 0]
        sortino_ratio = returns.mean() / downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 and downside_returns.std() > 0 else 0.0

        # Print backtest results
        logger.info(f"Backtest Results:")
        logger.info(f"Total Return: {total_return:.2%}")
        logger.info(f"Sharpe Ratio: {sharpe_ratio:.2f}")
        logger.info(f"Sortino Ratio: {sortino_ratio:.2f}")
        logger.info(f"Max Drawdown: {max_drawdown:.2%}")
        logger.info(f"Win Rate: {win_rate:.2%}")
        logger.info(f"Profit Factor: {profit_factor:.2f}")
        logger.info(f"Total Trades: {total_trades}")

        # Clean up backtest models
        for tf in self.timeframes:
            model_name = f"{self.symbol}_{tf}_backtest"
            self.model_manager.delete_model(model_name)

        return results
