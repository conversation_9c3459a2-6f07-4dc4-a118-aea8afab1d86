"""
ARIMA Model Module.
This module implements the ARIMA model for time series forecasting.
"""
import logging
import os
import pickle
from typing import Dict, Optional, Tuple
import numpy as np
import pandas as pd
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.stattools import adfuller

# Configure logger
logger = logging.getLogger('model.arima')

class ARIMAModel:
    """
    ARIMA Model class for time series forecasting.
    """
    def __init__(
        self,
        order: Tuple[int, int, int] = (1, 1, 0),
        seasonal_order: Tuple[int, int, int, int] = (0, 0, 0, 0)
    ):
        """
        Initialize ARIMAModel.

        Args:
            order: ARIMA order (p, d, q)
            seasonal_order: Seasonal order (P, D, Q, s)
        """
        self.order = order
        self.seasonal_order = seasonal_order
        self.model_fit = None

    def fit(self, y: np.ndarray, exog: Optional[np.ndarray] = None):
        """
        Fit the ARIMA model.

        Args:
            y: Time series data
            exog: Exogenous variables (optional)
        """
        # Convert to pandas Series
        y_series = pd.Series(y)

        # Create and fit model
        model = ARIMA(
            y_series,
            exog=exog,
            order=self.order,
            seasonal_order=self.seasonal_order
        )
        self.model_fit = model.fit()

        return self

    def predict(
        self,
        steps: int = 1,
        exog: Optional[np.ndarray] = None,
        X: Optional[np.ndarray] = None  # For compatibility with other models
    ) -> np.ndarray:
        """
        Make predictions with the ARIMA model.

        Args:
            steps: Number of steps to forecast
            exog: Exogenous variables for forecasting (optional)
            X: Input features (optional, for compatibility with other models)

        Returns:
            np.ndarray: Predictions
        """
        if self.model_fit is None:
            logger.error("Model not trained yet")
            return np.zeros((steps, 1))

        # If X is provided, use it to make predictions for each sample
        if X is not None:
            # Determine the number of samples to predict
            if isinstance(X, np.ndarray):
                if len(X.shape) > 1:
                    num_samples = X.shape[0]
                else:
                    num_samples = 1
            else:
                # If X is not a numpy array, try to determine its length
                try:
                    num_samples = len(X)
                except TypeError:
                    num_samples = 1

            # Create array to store predictions
            predictions = np.zeros((num_samples, 1))

            # For ARIMA models, we need to make predictions in a rolling fashion
            # to account for each sample in the test set

            # First, extract the time series from X if it's multi-dimensional
            if isinstance(X, np.ndarray) and len(X.shape) > 1:
                if X.shape[1] == 1:
                    # Single feature
                    time_series = X.flatten()
                else:
                    # Multiple features, use the last one as the target
                    time_series = X[:, -1]
            else:
                # X is already a 1D array or can be converted to one
                time_series = np.array(X).flatten() if X is not None else None

            # If we have a valid time series, make predictions for each sample
            if time_series is not None and len(time_series) > 0:
                # Create a copy of the model to avoid modifying the original
                import copy
                model_copy = copy.deepcopy(self.model_fit)

                # Make predictions for each sample
                for i in range(num_samples):
                    try:
                        # Make forecast for this sample
                        forecast = model_copy.forecast(steps=1, exog=exog[i:i+1] if exog is not None else None)
                        predictions[i, 0] = forecast.values[0]

                        # Update the model with the actual value for the next prediction
                        if i < num_samples - 1:
                            model_copy = model_copy.append([time_series[i]])
                    except Exception as e:
                        logger.warning(f"Error making prediction for sample {i}: {str(e)}")
                        # Use the last valid prediction or 0 if no valid predictions yet
                        if i > 0:
                            predictions[i, 0] = predictions[i-1, 0]

                return predictions

        # If X is not provided or we couldn't extract a time series, use the standard approach
        try:
            # Make forecast
            forecast = self.model_fit.forecast(steps=steps, exog=exog)

            # Reshape to match expected output format
            predictions = forecast.values.reshape(-1, 1)

            # If we need to match a specific number of samples, repeat the prediction
            if X is not None and isinstance(X, np.ndarray) and len(X.shape) > 0:
                num_samples = X.shape[0]
                if predictions.shape[0] == 1 and num_samples > 1:
                    predictions = np.repeat(predictions, num_samples, axis=0)

            return predictions

        except Exception as e:
            logger.error(f"Error making forecast: {str(e)}")
            # Return zeros with the appropriate shape
            if X is not None and isinstance(X, np.ndarray) and len(X.shape) > 0:
                return np.zeros((X.shape[0], 1))
            else:
                return np.zeros((steps, 1))

    def update(self, y_new: np.ndarray, exog_new: Optional[np.ndarray] = None):
        """
        Update the ARIMA model with new data.

        Args:
            y_new: New time series data
            exog_new: New exogenous variables (optional)
        """
        if self.model_fit is None:
            logger.error("Model not trained yet")
            return

        # Convert to pandas Series
        y_series = pd.Series(y_new)

        # Update model
        self.model_fit = self.model_fit.append(y_series, exog=exog_new)

# Try to import pmdarima for auto ARIMA model selection
PMDARIMA_AVAILABLE = False
try:
    from pmdarima import auto_arima
    PMDARIMA_AVAILABLE = True
except ImportError:
    # Define a fallback function if pmdarima is not installed
    def auto_arima(series, **kwargs):  # Use regular parameter names since they're documented
        """
        Fallback function if pmdarima is not available.

        Args:
            series: Time series data (unused in fallback)
            **kwargs: Additional parameters (unused in fallback)

        Returns:
            DummyModel: A simple model with default order (1,1,0)
        """
        # Suppress unused variable warnings by accessing the parameters
        _ = series, kwargs

        class DummyModel:
            def __init__(self, order):
                self.order = order

        # Only log this warning once per session
        if not hasattr(auto_arima, '_warning_logged'):
            logger.warning("pmdarima not installed, using default ARIMA parameters (1,1,0). "
                          "Install pmdarima for automatic model selection: pip install pmdarima>=2.0.0")
            auto_arima._warning_logged = True

        return DummyModel((1, 1, 0))

class ARIMAModelTrainer:
    """
    ARIMA Model Trainer class for training and evaluating ARIMA models.
    """
    def __init__(
        self,
        order: Tuple[int, int, int] = None,  # Default: auto-determine
        seasonal_order: Tuple[int, int, int, int] = (0, 0, 0, 0),  # Default: no seasonality
        auto_determine: bool = True
    ):
        """
        Initialize ARIMAModelTrainer.

        Args:
            order: ARIMA order (p, d, q)
            seasonal_order: Seasonal order (P, D, Q, s)
            auto_determine: Whether to automatically determine the order
        """
        # Save hyperparameters
        self.hyperparams = {
            'order': order,
            'seasonal_order': seasonal_order,
            'auto_determine': auto_determine
        }

        # Store model parameters directly as attributes
        self.order = order
        self.seasonal_order = seasonal_order
        self.auto_determine = auto_determine

        # Initialize model to None (will be created during training)
        self.model = None
        self.model_fit = None

        # Initialize history
        self.history = {
            'train_loss': [],
            'test_loss': None,
            'aic': None,
            'bic': None
        }

    def _check_stationarity(self, series: pd.Series) -> Tuple[bool, int]:
        """
        Check if a time series is stationary using ADF test.

        Args:
            series: Time series data

        Returns:
            Tuple[bool, int]: (is_stationary, recommended_d_value)
        """
        try:
            # Perform ADF test
            result = adfuller(series.dropna())
            p_value = result[1]

            # Check if stationary (p-value < 0.05)
            is_stationary = p_value < 0.05

            # Recommend d value
            if is_stationary:
                return True, 0
            else:
                # Try differencing once
                diff_once = series.diff().dropna()
                result_diff = adfuller(diff_once)
                p_value_diff = result_diff[1]

                if p_value_diff < 0.05:
                    return False, 1
                else:
                    # Try differencing twice
                    diff_twice = diff_once.diff().dropna()
                    result_diff2 = adfuller(diff_twice)
                    p_value_diff2 = result_diff2[1]

                    if p_value_diff2 < 0.05:
                        return False, 2
                    else:
                        return False, 1  # Default to 1 if still not stationary
        except Exception as e:
            logger.error(f"Error checking stationarity: {str(e)}")
            return False, 1  # Default to 1 in case of error

    def _auto_determine_order(self, series: pd.Series) -> Tuple[int, int, int]:
        """
        Automatically determine the ARIMA order using auto_arima.

        Args:
            series: Time series data

        Returns:
            Tuple[int, int, int]: ARIMA order (p, d, q)
        """
        try:
            if not PMDARIMA_AVAILABLE:
                # If pmdarima is not available, use ADF test to determine d
                _, d = self._check_stationarity(series)

                # Try to determine p and q using ACF and PACF
                try:
                    from statsmodels.tsa.stattools import acf, pacf

                    # Difference the series if needed
                    if d > 0:
                        diff_series = series.diff(d).dropna()
                    else:
                        diff_series = series

                    # Calculate ACF and PACF
                    acf_values = acf(diff_series, nlags=10)
                    pacf_values = pacf(diff_series, nlags=10)

                    # Determine p from PACF
                    # Look for the first lag where PACF crosses the significance threshold
                    significance_threshold = 1.96 / np.sqrt(len(diff_series))
                    p = 1  # Default
                    for i in range(1, len(pacf_values)):
                        if abs(pacf_values[i]) < significance_threshold:
                            p = max(1, i - 1)  # Ensure p is at least 1
                            break

                    # Determine q from ACF
                    # Look for the first lag where ACF crosses the significance threshold
                    q = 0  # Default
                    for i in range(1, len(acf_values)):
                        if abs(acf_values[i]) < significance_threshold:
                            q = max(0, i - 1)  # q can be 0
                            break

                    # Limit p and q to reasonable values
                    p = min(p, 2)
                    q = min(q, 2)

                except Exception as acf_error:
                    logger.warning(f"Error determining p and q from ACF/PACF: {str(acf_error)}. Using defaults p=1, q=0.")
                    # Use default values for p and q
                    p, q = 1, 0

                logger.info(f"Using basic stationarity test to determine d={d}, with p={p}, q={q}")
                return (p, d, q)

            # Use auto_arima to determine the best order with more robust settings
            try:
                model = auto_arima(
                    series,
                    seasonal=False,  # No seasonality
                    stepwise=True,   # Use stepwise algorithm
                    suppress_warnings=True,
                    error_action='ignore',
                    max_p=3,         # Limit max order to prevent overfitting
                    max_d=2,
                    max_q=3,
                    max_order=6,     # Limit total order
                    trace=False,
                    random_state=42, # For reproducibility
                    n_fits=10        # Limit number of fits for speed
                )

                # Get the order
                order = model.order
                logger.info(f"Auto-determined ARIMA order: {order}")

                return order
            except Exception as auto_error:
                logger.warning(f"Error in auto_arima: {str(auto_error)}. Falling back to basic method.")
                # Fall back to basic method
                _, d = self._check_stationarity(series)
                p, q = 1, 0
                logger.info(f"Falling back to basic order determination: p={p}, d={d}, q={q}")
                return (p, d, q)

        except Exception as e:
            logger.error(f"Error auto-determining ARIMA order: {str(e)}")
            # Default to ARIMA(1,1,0) in case of error
            return (1, 1, 0)

    def fit(self, y_train: np.ndarray, exog_train: Optional[np.ndarray] = None) -> bool:
        """
        Fit the ARIMA model with initial data.

        Args:
            y_train: Training target values
            exog_train: Exogenous variables for training (optional)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create a proper time index for the Series
            # This is crucial for ARIMA forecasting
            try:
                # First try to create a date range index
                index = pd.date_range(
                    start=pd.Timestamp('2020-01-01'),  # Arbitrary start date
                    periods=len(y_train),
                    freq='D'  # Daily frequency
                )

                # Convert to pandas Series with proper index
                y_series = pd.Series(y_train, index=index)
            except ValueError as ve:
                # If we get a duplicate index error, use a simple range index instead
                if "duplicate" in str(ve).lower():
                    logger.warning("Duplicate index detected, using integer index instead")
                    y_series = pd.Series(y_train, index=range(len(y_train)))
                else:
                    # Re-raise other errors
                    raise

            # Determine order if auto_determine is True
            if self.auto_determine or self.order is None:
                self.order = self._auto_determine_order(y_series)
                self.hyperparams['order'] = self.order

            # Create and fit model
            self.model = ARIMA(
                y_series,
                exog=exog_train,
                order=self.order,
                seasonal_order=self.seasonal_order
            )
            self.model_fit = self.model.fit()

            # Save metrics
            self.history['aic'] = float(self.model_fit.aic)
            self.history['bic'] = float(self.model_fit.bic)
            self.history['train_loss'] = [float(self.model_fit.mse)]

            # Add model parameters to history
            self.history['order'] = self.order
            self.history['seasonal_order'] = self.seasonal_order

            logger.info(f"ARIMA model trained with order {self.order}, AIC: {self.history['aic']:.2f}")

            return True

        except Exception as e:
            logger.error(f"Error training ARIMA model: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def get_aic(self) -> float:
        """
        Get the AIC value of the model.

        Returns:
            float: AIC value
        """
        if self.model_fit is not None:
            return self.model_fit.aic
        return float('inf')

    def get_params(self) -> Dict:
        """
        Get the model parameters.

        Returns:
            Dict: Model parameters
        """
        return {
            'order': self.order,
            'seasonal_order': self.seasonal_order,
            'auto_determine': self.auto_determine
        }

    def train(
        self,
        y_train: np.ndarray,
        exog_train: Optional[np.ndarray] = None
    ) -> Dict:
        """
        Train the ARIMA model.

        Args:
            y_train: Training target values
            exog_train: Exogenous variables for training (optional)

        Returns:
            Dict: Training history
        """
        try:
            # For ARIMA models, we need to ensure we have a valid index without duplicates
            # First, check if y_train is already a pandas Series with a valid index
            if isinstance(y_train, pd.Series) and y_train.index.is_unique:
                # Use the existing Series directly
                y_series = y_train
                logger.info("Using existing pandas Series with valid index")
            else:
                # Convert to numpy array if it's a Series with duplicate index
                if isinstance(y_train, pd.Series):
                    y_train = y_train.values
                    logger.warning("Converting Series with duplicate index to numpy array")

                # Create a simple integer index which is guaranteed to be unique
                try:
                    # Create a guaranteed unique RangeIndex
                    index = pd.RangeIndex(start=0, stop=len(y_train))
                    y_series = pd.Series(y_train, index=index)
                    logger.info("Created Series with integer index")
                except Exception as e:
                    # Last resort: create a list and convert to Series
                    logger.warning(f"Error creating Series with RangeIndex: {str(e)}. Using list conversion.")
                    y_list = [float(val) for val in y_train]
                    y_series = pd.Series(y_list)

            # Determine order if auto_determine is True
            if self.auto_determine or self.order is None:
                self.order = self._auto_determine_order(y_series)
                self.hyperparams['order'] = self.order

            # Create and fit model with improved convergence settings
            self.model = ARIMA(
                y_series,
                exog=exog_train,
                order=self.order,
                seasonal_order=self.seasonal_order
            )

            # Try different fitting methods to improve convergence
            try:
                # First try with default settings
                self.model_fit = self.model.fit()
            except Exception as e:
                logger.warning(f"First ARIMA fit attempt failed: {str(e)}. Trying with method='lbfgs'")
                try:
                    # Try with LBFGS method which can be more robust
                    self.model_fit = self.model.fit(method='lbfgs', maxiter=500)
                except Exception as e2:
                    logger.warning(f"Second ARIMA fit attempt failed: {str(e2)}. Trying with method='powell'")
                    # Last resort: use powell method which is slower but more robust
                    self.model_fit = self.model.fit(method='powell', maxiter=500)

            # Save metrics
            self.history['aic'] = float(self.model_fit.aic)
            self.history['bic'] = float(self.model_fit.bic)
            self.history['train_loss'] = [float(self.model_fit.mse)]

            # Add model parameters to history
            self.history['order'] = self.order
            self.history['seasonal_order'] = self.seasonal_order

            logger.info(f"ARIMA model trained with order {self.order}, AIC: {self.history['aic']:.2f}")

            return self.history

        except Exception as e:
            logger.error(f"Error training ARIMA model: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # Return a valid history even on error
            if 'aic' not in self.history:
                self.history['aic'] = 0.0
            if 'bic' not in self.history:
                self.history['bic'] = 0.0
            if 'train_loss' not in self.history or not self.history['train_loss']:
                self.history['train_loss'] = [0.0]
            return self.history

    def predict(
        self,
        steps: int = 1,
        exog: Optional[np.ndarray] = None,
        return_conf_int: bool = False,
        alpha: float = 0.05,
        X: Optional[np.ndarray] = None  # Added X parameter for compatibility with other models
    ) -> np.ndarray:
        """
        Make predictions with the ARIMA model.

        Args:
            steps: Number of steps to forecast
            exog: Exogenous variables for forecasting (optional)
            return_conf_int: Whether to return confidence intervals
            alpha: Significance level for confidence intervals
            X: Input features (optional, for compatibility with other models)

        Returns:
            np.ndarray: Predictions or (predictions, (lower_ci, upper_ci)) if return_conf_int=True
        """
        return self.forecast(steps, X, exog, return_conf_int, alpha)

    def forecast(
        self,
        steps: int = 1,
        X: Optional[np.ndarray] = None,
        exog: Optional[np.ndarray] = None,
        return_conf_int: bool = False,
        alpha: float = 0.05
    ) -> np.ndarray:
        """
        Make predictions with the ARIMA model.

        Args:
            steps: Number of steps to forecast
            X: Input features (optional, for compatibility with other models)
            exog: Exogenous variables for forecasting (optional)
            return_conf_int: Whether to return confidence intervals
            alpha: Significance level for confidence intervals

        Returns:
            np.ndarray: Predictions or (predictions, (lower_ci, upper_ci)) if return_conf_int=True
        """
        try:
            if self.model_fit is None:
                logger.error("Model not trained yet")
                return np.zeros(steps)

            # If X is provided, use it as the time series data
            if X is not None:
                # Update the model with the new data
                # This is a simplified approach - in a real implementation, you might want to
                # retrain the model or use a more sophisticated update method
                if isinstance(X, np.ndarray):
                    if len(X.shape) > 1:
                        # If X is multi-dimensional, try to extract the time series
                        if X.shape[1] == 1:
                            # Single feature
                            time_series = X.flatten()
                        else:
                            # Multiple features, use the last one as the target
                            time_series = X[:, -1]
                    else:
                        # X is already a 1D array
                        time_series = X
                else:
                    # If X is not a numpy array, try to convert it
                    time_series = np.array(X)

                # Update the model with the new data
                self.update(time_series)

            # Make forecast
            try:
                if return_conf_int:
                    forecast, conf_int = self.model_fit.forecast(steps=steps, exog=exog, alpha=alpha)

                    # Handle different return types
                    if isinstance(forecast, pd.Series):
                        forecast_values = forecast.values
                    else:
                        forecast_values = np.array(forecast)

                    return forecast_values.reshape(-1, 1), conf_int
                else:
                    forecast = self.model_fit.forecast(steps=steps, exog=exog)

                    # Handle different return types
                    if isinstance(forecast, pd.Series):
                        forecast_values = forecast.values
                    else:
                        forecast_values = np.array(forecast)

                    return forecast_values.reshape(-1, 1)
            except Exception as forecast_error:
                logger.warning(f"Error in forecast method: {str(forecast_error)}")

                # Try alternative prediction method
                logger.info("Trying alternative prediction method...")

                try:
                    # Get the last index from the model
                    if hasattr(self.model_fit, 'model') and hasattr(self.model_fit.model, 'data'):
                        last_idx = self.model_fit.model.data.orig_endog.index[-1]

                        # Create a future index for prediction
                        if isinstance(last_idx, pd.Timestamp):
                            freq = pd.infer_freq(self.model_fit.model.data.orig_endog.index)
                            if freq is not None:
                                # Create a future index for prediction
                                # (not used directly but kept for future enhancements)
                                _ = pd.date_range(
                                    start=last_idx + pd.Timedelta(freq),
                                    periods=steps,
                                    freq=freq
                                )
                            else:
                                # Default to daily frequency
                                # (not used directly but kept for future enhancements)
                                _ = pd.date_range(
                                    start=last_idx + pd.Timedelta(days=1),
                                    periods=steps,
                                    freq='D'
                                )
                        else:
                            # Integer index
                            # (not used directly but kept for future enhancements)
                            _ = range(
                                len(self.model_fit.model.data.orig_endog),
                                len(self.model_fit.model.data.orig_endog) + steps
                            )
                    else:
                        # Create a default index
                        # (not used directly but kept for future enhancements)
                        _ = range(steps)

                    # Use predict method instead of forecast
                    predictions = self.model_fit.predict(
                        start=len(self.model_fit.model.data.orig_endog),
                        end=len(self.model_fit.model.data.orig_endog) + steps - 1,
                        exog=exog
                    )

                    # Return predictions
                    if return_conf_int:
                        # Create dummy confidence intervals
                        conf_int = np.array([
                            [pred - 0.1, pred + 0.1] for pred in predictions
                        ])
                        return predictions.reshape(-1, 1), conf_int
                    else:
                        return predictions.reshape(-1, 1)
                except Exception as predict_error:
                    logger.error(f"Alternative prediction method also failed: {str(predict_error)}")
                    # Return zeros as a last resort
                    return np.zeros((steps, 1))

        except Exception as e:
            logger.error(f"Error making predictions with ARIMA model: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return np.zeros((steps, 1))

    def update(
        self,
        y_new: np.ndarray,
        exog_new: Optional[np.ndarray] = None
    ) -> bool:
        """
        Update the ARIMA model with new data.

        Args:
            y_new: New target values
            exog_new: New exogenous variables (optional)

        Returns:
            bool: True if update was successful, False otherwise
        """
        try:
            if self.model_fit is None:
                logger.error("Model not trained yet")
                return False

            # Ensure y_new is 1D
            if isinstance(y_new, np.ndarray) and y_new.ndim > 1:
                # Flatten if it's a multi-dimensional array
                y_new = y_new.flatten()
                logger.warning(f"Flattened multi-dimensional input array to 1D shape: {y_new.shape}")

            # Convert to list if needed to ensure compatibility
            if not isinstance(y_new, (list, pd.Series)):
                try:
                    y_new = y_new.tolist()
                except:
                    y_new = [float(y) for y in y_new]

            # Get the last index from the model
            if hasattr(self.model_fit, 'data') and hasattr(self.model_fit.data, 'row_labels'):
                last_idx = self.model_fit.data.row_labels[-1]
                # Create a proper index that extends the model's index
                if isinstance(last_idx, (int, np.integer)):
                    # If the index is integer-based
                    new_idx = pd.RangeIndex(start=last_idx + 1, stop=last_idx + 1 + len(y_new))
                elif isinstance(last_idx, pd.Timestamp):
                    # If the index is timestamp-based
                    freq = pd.infer_freq(self.model_fit.data.row_labels)
                    if freq is not None:
                        # Map timeframe strings to valid pandas frequency strings if needed
                        if freq in ['M5', 'M15', 'M30', 'H1', 'H4']:
                            # Convert MT5 timeframe format to pandas frequency format
                            if freq.startswith('M'):
                                # Convert minutes format (e.g., 'M15' to '15min')
                                minutes = int(freq[1:])
                                pandas_freq = f'{minutes}min'
                            elif freq.startswith('H'):
                                # Convert hours format (e.g., 'H1' to '1H')
                                hours = int(freq[1:])
                                pandas_freq = f'{hours}H'
                            else:
                                # Default fallback
                                pandas_freq = '1min'

                            logger.info(f"Converted timeframe '{freq}' to pandas frequency '{pandas_freq}'")
                            new_idx = pd.date_range(start=last_idx, periods=len(y_new) + 1, freq=pandas_freq)[1:]
                        else:
                            # Use the inferred frequency
                            try:
                                new_idx = pd.date_range(start=last_idx + pd.Timedelta(freq), periods=len(y_new), freq=freq)
                            except (ValueError, pd._libs.tslibs.np_datetime.OutOfBoundsDatetime):
                                # If there's an error with the frequency or date range, use integer index
                                logger.warning(f"Error with frequency '{freq}', using integer index")
                                new_idx = pd.RangeIndex(start=len(self.model_fit.data.row_labels), stop=len(self.model_fit.data.row_labels) + len(y_new))
                    else:
                        # If frequency can't be inferred, use integer index
                        new_idx = pd.RangeIndex(start=len(self.model_fit.data.row_labels), stop=len(self.model_fit.data.row_labels) + len(y_new))
                else:
                    # Fallback to integer index
                    new_idx = pd.RangeIndex(start=len(self.model_fit.data.row_labels), stop=len(self.model_fit.data.row_labels) + len(y_new))
            else:
                # Fallback to integer index
                new_idx = pd.RangeIndex(start=0, stop=len(y_new))

            # Convert to pandas Series with proper index and ensure numeric dtype
            try:
                # Ensure y_new is numeric and not object dtype
                y_numeric = pd.to_numeric(y_new, errors='coerce')

                # Check for any NaN values that might have been created during conversion
                if y_numeric.isna().any():
                    logger.warning(f"Found NaN values after numeric conversion, filling with 0")
                    y_numeric = y_numeric.fillna(0)

                y_series = pd.Series(y_numeric, index=new_idx, dtype='float64')
            except Exception as e:
                logger.warning(f"Error creating Series with index: {str(e)}. Using simple Series.")
                # Fallback: ensure numeric dtype even in simple case
                y_numeric = pd.to_numeric(y_new, errors='coerce').fillna(0)
                y_series = pd.Series(y_numeric, dtype='float64')

            # Ensure exog_new is also properly formatted if provided
            if exog_new is not None:
                try:
                    # Convert exog_new to numeric DataFrame if it's not already
                    if isinstance(exog_new, np.ndarray):
                        exog_new = pd.DataFrame(exog_new, index=new_idx)
                    elif isinstance(exog_new, pd.DataFrame):
                        # Ensure numeric dtypes
                        exog_new = exog_new.apply(pd.to_numeric, errors='coerce').fillna(0)
                    else:
                        # Convert to DataFrame
                        exog_new = pd.DataFrame(exog_new, index=new_idx)
                        exog_new = exog_new.apply(pd.to_numeric, errors='coerce').fillna(0)
                except Exception as e:
                    logger.warning(f"Error processing exog_new: {str(e)}. Setting to None.")
                    exog_new = None

            # Update model
            try:
                self.model_fit = self.model_fit.append(y_series, exog=exog_new)
                logger.info(f"ARIMA model updated with {len(y_new)} new observations")
                return True
            except ValueError as ve:
                if "Given `endog` does not have an index" in str(ve):
                    # If we still have index issues, retrain the model with all data
                    logger.warning(f"Index error when updating model, retraining with all data: {str(ve)}")
                    # Get all existing data
                    if hasattr(self.model_fit, 'data') and hasattr(self.model_fit.data, 'endog'):
                        existing_data = self.model_fit.data.endog
                        # Combine with new data
                        all_data = np.concatenate([existing_data, y_new])
                        # Create a new model with all data
                        self.fit(all_data)
                        logger.info(f"ARIMA model retrained with {len(all_data)} observations")
                        return True
                    else:
                        logger.error("Could not access existing data for retraining")
                        return False
                else:
                    # Re-raise other ValueError exceptions
                    raise

        except Exception as e:
            logger.error(f"Error updating ARIMA model: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def evaluate(
        self,
        y_test: np.ndarray,
        exog_test: Optional[np.ndarray] = None
    ) -> Dict[str, float]:
        """
        Evaluate the ARIMA model on test data.

        Args:
            y_test: Test target values
            exog_test: Test exogenous variables (optional)

        Returns:
            Dict[str, float]: Evaluation metrics
        """
        try:
            if self.model_fit is None:
                logger.error("Model not trained yet")
                return {}

            # Make one-step forecasts
            forecasts = []
            for i in range(len(y_test)):
                # Get exog for this step if provided
                exog_i = None if exog_test is None else exog_test[i:i+1]

                try:
                    # Make forecast
                    forecast = self.model_fit.forecast(steps=1, exog=exog_i)
                    # Handle different return types from forecast
                    if isinstance(forecast, pd.Series):
                        forecasts.append(forecast.iloc[0])
                    elif isinstance(forecast, np.ndarray):
                        forecasts.append(forecast[0])
                    else:
                        forecasts.append(float(forecast))
                except Exception as forecast_error:
                    logger.warning(f"Error making forecast at step {i}: {str(forecast_error)}")
                    # Use previous forecast or 0 if no previous forecast
                    forecasts.append(forecasts[-1] if forecasts else 0.0)

                # Update model with actual value (safely)
                if i < len(y_test) - 1:
                    try:
                        # Try to create a Series with a proper index that extends the model's index
                        try:
                            # Get the last index from the model
                            if hasattr(self.model_fit, 'model') and hasattr(self.model_fit.model, 'data'):
                                # Get the model's index information
                                model_index = self.model_fit.model.data.row_labels
                                last_idx = model_index[-1]

                                # Create a proper index that extends the model's index
                                if isinstance(last_idx, pd.Timestamp):
                                    # If the index is timestamp-based
                                    freq = pd.infer_freq(model_index)
                                    if freq is not None:
                                        # Map timeframe strings to valid pandas frequency strings if needed
                                        if freq in ['M5', 'M15', 'M30', 'H1', 'H4']:
                                            # Convert MT5 timeframe format to pandas frequency format
                                            if freq.startswith('M'):
                                                # Convert minutes format (e.g., 'M15' to '15min')
                                                minutes = int(freq[1:])
                                                pandas_freq = f'{minutes}min'
                                            elif freq.startswith('H'):
                                                # Convert hours format (e.g., 'H1' to '1H')
                                                hours = int(freq[1:])
                                                pandas_freq = f'{hours}H'
                                            else:
                                                # Default fallback
                                                pandas_freq = '1min'

                                            # Create a new timestamp with the correct frequency
                                            new_idx = pd.date_range(
                                                start=last_idx,
                                                periods=2,
                                                freq=pandas_freq
                                            )[1:]
                                        else:
                                            # Use the inferred frequency
                                            try:
                                                new_idx = pd.date_range(
                                                    start=last_idx + pd.Timedelta(freq),
                                                    periods=1,
                                                    freq=freq
                                                )
                                            except (ValueError, pd._libs.tslibs.np_datetime.OutOfBoundsDatetime):
                                                # If there's an error with the frequency or date range, use integer index
                                                logger.debug(f"Error with frequency '{freq}', using integer index")
                                                new_idx = pd.Index([len(model_index)])

                                        # Create Series with proper index
                                        y_i = pd.Series([y_test[i]], index=new_idx)

                                        # Use extend method instead of append (which is deprecated)
                                        try:
                                            self.model_fit = self.model_fit.extend(y_i)
                                        except (AttributeError, TypeError):
                                            # Fallback to append if extend is not available
                                            self.model_fit = self.model_fit.append(y_i)
                                    else:
                                        # If we can't determine frequency, skip updating
                                        # This avoids the "Expected index frequency" warnings
                                        logger.debug(f"Skipping update at step {i}: Unable to determine frequency")
                                else:
                                    # For integer index, just skip updating
                                    # This is safer than trying to extend with a different index type
                                    logger.debug(f"Skipping update at step {i}: Non-timestamp index")
                            else:
                                # If we can't access the model's index, skip updating
                                logger.debug(f"Skipping update at step {i}: Cannot access model index")
                        except Exception as idx_error:
                            # If we still have index issues, just skip updating
                            logger.debug(f"Skipping update due to index error: {str(idx_error)}")
                    except Exception as update_error:
                        # Log at debug level instead of warning to reduce noise
                        logger.debug(f"Error updating model at step {i}: {str(update_error)}")
                        # Continue without updating

            # Convert to numpy array
            forecasts = np.array(forecasts)

            # Calculate metrics
            mse = np.mean((forecasts - y_test) ** 2)
            rmse = np.sqrt(mse)
            mae = np.mean(np.abs(forecasts - y_test))

            # Calculate direction accuracy
            direction_accuracy = np.mean((np.sign(forecasts) == np.sign(y_test)).astype(int))

            # Calculate R-squared (coefficient of determination)
            # R² = 1 - (sum of squared residuals / total sum of squares)
            ss_res = np.sum((y_test - forecasts) ** 2)
            ss_tot = np.sum((y_test - np.mean(y_test)) ** 2)
            r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0.0

            metrics = {
                'mse': float(mse),
                'rmse': float(rmse),
                'mae': float(mae),
                'direction_accuracy': float(direction_accuracy),
                'r2': float(r2)
            }

            logger.info(f"Evaluation metrics: MSE={mse:.6f}, RMSE={rmse:.6f}, MAE={mae:.6f}, "
                       f"Direction Accuracy={direction_accuracy:.6f}, R2={r2:.6f}")

            return metrics

        except Exception as e:
            logger.error(f"Error evaluating ARIMA model: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # Return default metrics instead of empty dict
            return {
                'mse': 0.0,
                'rmse': 0.0,
                'mae': 0.0,
                'direction_accuracy': 0.5,  # Default to random guessing
                'r2': 0.0  # Default to no fit
            }

    def save_model(self, filepath: str):
        """
        Save the ARIMA model.

        Args:
            filepath: Path to save the model
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)

            if self.model_fit is not None:
                # Save model using pickle
                with open(filepath, 'wb') as f:
                    pickle.dump({
                        'model_fit': self.model_fit,
                        'hyperparams': self.hyperparams,
                        'history': self.history
                    }, f)
                logger.info(f"ARIMA model saved to {filepath}")
            else:
                logger.error("Cannot save model: Model not trained yet")

        except Exception as e:
            logger.error(f"Error saving ARIMA model: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    def load_model(self, filepath: str) -> bool:
        """
        Load the ARIMA model.

        Args:
            filepath: Path to load the model from

        Returns:
            bool: True if model was loaded successfully, False otherwise
        """
        try:
            # Check if file exists
            if not os.path.exists(filepath):
                logger.error(f"Model file {filepath} not found")
                return False

            # Load model
            with open(filepath, 'rb') as f:
                data = pickle.load(f)

            # Set attributes
            self.model_fit = data['model_fit']
            self.hyperparams = data['hyperparams']
            self.order = self.hyperparams['order']
            self.seasonal_order = self.hyperparams['seasonal_order']
            self.auto_determine = self.hyperparams['auto_determine']
            self.history = data['history']

            logger.info(f"ARIMA model loaded from {filepath}")
            return True

        except Exception as e:
            logger.error(f"Error loading ARIMA model: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False
