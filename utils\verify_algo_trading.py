#!/usr/bin/env python3
"""
Helper script to verify MT5 Algo Trading status.
Used by the optimized batch file launcher.
"""

import sys
import os

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def verify_algo_trading():
    """Verify MT5 Algo Trading status for all terminals."""
    try:
        from utils.algo_trading_manager import verify_all_terminals
        
        print('[INFO] Checking Algo Trading status for all terminals...')
        result = verify_all_terminals()
        
        if result:
            print('[SUCCESS] All MT5 terminals have Algo Trading enabled')
            return True
        else:
            print('[WARNING] Some MT5 terminals may not have Algo Trading enabled')
            print('[WARNING] Please check the MT5 Algo Trading Maintenance window')
            print('[WARNING] You may need to manually enable Algo Trading in MT5')
            print('[WARNING] Continuing anyway...')
            return False
            
    except Exception as e:
        print(f'[ERROR] Error checking Algo Trading status: {str(e)}')
        print('[WARNING] Continuing anyway...')
        return False

if __name__ == "__main__":
    success = verify_algo_trading()
    sys.exit(0 if success else 1)
