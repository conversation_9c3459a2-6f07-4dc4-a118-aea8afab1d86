#!/usr/bin/env python3
"""
Test script to verify the LSTM training and ARIMA ensemble fixes.
This script tests the critical issues identified in the log analysis.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import torch
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_lstm_training():
    """Test LSTM training with gradient clipping fixes."""
    logger.info("Testing LSTM training with gradient clipping fixes...")
    
    try:
        from model.model_trainer import ModelTrainer
        
        # Create synthetic data for testing
        sequence_length = 10
        num_features = 109  # Same as in the logs
        num_samples = 1000
        
        # Generate synthetic time series data
        np.random.seed(42)
        X_train = np.random.randn(num_samples, sequence_length, num_features).astype(np.float32)
        y_train = np.random.randn(num_samples).astype(np.float32)
        
        X_val = np.random.randn(200, sequence_length, num_features).astype(np.float32)
        y_val = np.random.randn(200).astype(np.float32)
        
        logger.info(f"Created synthetic data: X_train shape: {X_train.shape}, y_train shape: {y_train.shape}")
        
        # Create LSTM trainer
        trainer = ModelTrainer(
            input_size=num_features,
            hidden_size=64,
            num_layers=2,
            output_size=1,
            learning_rate=0.001,  # Conservative learning rate
            weight_decay=1e-5
        )
        
        logger.info("Created LSTM trainer")
        
        # Train for a few epochs to test gradient clipping
        history = trainer.train(
            X_train=X_train,
            y_train=y_train,
            X_val=X_val,
            y_val=y_val,
            batch_size=32,
            epochs=5,  # Just a few epochs for testing
            patience=10,
            verbose=True
        )
        
        # Check if training completed without NaN losses
        if history and 'train_loss' in history:
            train_losses = history['train_loss']
            val_losses = history['val_loss']
            
            # Check for NaN values
            nan_train = any(np.isnan(loss) for loss in train_losses if loss is not None)
            nan_val = any(np.isnan(loss) for loss in val_losses if loss is not None)
            
            if nan_train or nan_val:
                logger.error("❌ LSTM training still produces NaN losses!")
                logger.error(f"Train losses: {train_losses}")
                logger.error(f"Val losses: {val_losses}")
                return False
            else:
                logger.info("✅ LSTM training completed without NaN losses!")
                logger.info(f"Final train loss: {train_losses[-1]:.6f}")
                logger.info(f"Final val loss: {val_losses[-1]:.6f}")
                return True
        else:
            logger.error("❌ LSTM training failed - no history returned")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing LSTM training: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_arima_data_types():
    """Test ARIMA model with proper data type handling."""
    logger.info("Testing ARIMA model with data type fixes...")
    
    try:
        from model.arima_model import ARIMAModelTrainer
        
        # Create synthetic time series data with potential object dtype issues
        np.random.seed(42)
        
        # Create data that might cause object dtype issues
        y_data = np.random.randn(100).astype(np.float64)
        
        # Add some potential problematic values
        y_mixed = []
        for i, val in enumerate(y_data):
            if i % 20 == 0:
                # Add some values that might cause dtype issues
                y_mixed.append(str(val))  # String representation
            else:
                y_mixed.append(val)
        
        y_mixed = np.array(y_mixed, dtype=object)  # This creates object dtype
        
        logger.info(f"Created mixed-type data: dtype={y_mixed.dtype}, shape={y_mixed.shape}")
        
        # Create ARIMA trainer
        trainer = ARIMAModelTrainer(
            order=(1, 0, 1),
            auto_determine=False
        )
        
        logger.info("Created ARIMA trainer")
        
        # Train ARIMA model
        success = trainer.fit(y_mixed)
        
        if not success:
            logger.error("❌ ARIMA training failed")
            return False
        
        logger.info("✅ ARIMA training completed successfully")
        
        # Test update with new mixed-type data
        y_new_mixed = np.array([str(x) if i % 5 == 0 else x for i, x in enumerate(np.random.randn(10))], dtype=object)
        
        logger.info(f"Testing update with new mixed data: dtype={y_new_mixed.dtype}")
        
        update_success = trainer.update(y_new_mixed)
        
        if update_success:
            logger.info("✅ ARIMA update completed successfully - data type fix working!")
            return True
        else:
            logger.error("❌ ARIMA update failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing ARIMA data types: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_ensemble_model():
    """Test ensemble model with LSTM+ARIMA combination."""
    logger.info("Testing ensemble model (LSTM+ARIMA)...")
    
    try:
        from model.model_trainer import ModelManager
        
        # Create synthetic data
        np.random.seed(42)
        sequence_length = 10
        num_features = 50  # Smaller for testing
        num_samples = 500
        
        X_train = np.random.randn(num_samples, sequence_length, num_features).astype(np.float32)
        y_train = np.random.randn(num_samples).astype(np.float32)
        
        X_val = np.random.randn(100, sequence_length, num_features).astype(np.float32)
        y_val = np.random.randn(100).astype(np.float32)
        
        logger.info(f"Created ensemble test data: X_train shape: {X_train.shape}")
        
        # Create model manager
        manager = ModelManager()
        
        # Create ensemble model
        ensemble_trainer = manager.create_model(
            model_name="test_ensemble",
            model_type="lstm_arima",
            input_size=num_features,
            hidden_size=32,  # Smaller for testing
            num_layers=1,
            output_size=1
        )
        
        logger.info("Created ensemble model")
        
        # Train ensemble model
        history = manager.train_model(
            model_name="test_ensemble",
            X_train=X_train,
            y_train=y_train,
            X_val=X_val,
            y_val=y_val
        )
        
        if history:
            logger.info("✅ Ensemble model training completed successfully!")
            return True
        else:
            logger.error("❌ Ensemble model training failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing ensemble model: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_batch_file_dependencies():
    """Test that all dependencies for the batch file are available."""
    logger.info("Testing batch file dependencies...")
    
    try:
        # Test Python imports
        import MetaTrader5 as mt5
        logger.info("✅ MetaTrader5 import successful")
        
        import pandas as pd
        logger.info("✅ Pandas import successful")
        
        import numpy as np
        logger.info("✅ NumPy import successful")
        
        import torch
        logger.info("✅ PyTorch import successful")
        
        from sklearn.metrics import mean_squared_error
        logger.info("✅ Scikit-learn import successful")
        
        # Test custom modules
        from config.credentials import MT5_TERMINALS
        logger.info(f"✅ MT5 terminals configuration loaded: {len(MT5_TERMINALS)} terminals")
        
        from utils.algo_trading_manager import verify_all_terminals, keep_algo_trading_enabled
        logger.info("✅ Algo trading manager import successful")
        
        from model.model_trainer import ModelTrainer, ModelManager
        logger.info("✅ Model trainer import successful")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Dependency test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    logger.info("="*80)
    logger.info("BTCUSD TRADING BOT - MODEL FIXES VERIFICATION")
    logger.info("="*80)
    
    tests = [
        ("Batch File Dependencies", test_batch_file_dependencies),
        ("LSTM Training (NaN Fix)", test_lstm_training),
        ("ARIMA Data Types (Object Fix)", test_arima_data_types),
        ("Ensemble Model Integration", test_ensemble_model),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {str(e)}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*80}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*80}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name:<30} {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! The fixes are working correctly.")
        return True
    else:
        logger.error(f"⚠️  {total - passed} tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
