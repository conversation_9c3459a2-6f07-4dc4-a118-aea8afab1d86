@echo off
setlocal enabledelayedexpansion

echo ================================================================================
echo                      MT5 ALGO TRADING MAINTENANCE SERVICE
echo ================================================================================
echo.
echo [INFO] This window keeps MT5 Algo Trading enabled across all terminals
echo [INFO] DO NOT CLOSE this window while trading
echo [INFO] Press Ctrl+C to stop the maintenance service
echo.

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo [INFO] Script directory: %SCRIPT_DIR%
echo [INFO] Current directory: %CD%
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo [ERROR] Please install Python and add it to your PATH
    pause
    exit /b 1
)

echo [INFO] Python version:
python --version
echo.

REM Check if required Python files exist
if not exist "utils\algo_trading_manager.py" (
    echo [ERROR] utils\algo_trading_manager.py not found
    echo [ERROR] Please ensure the utils directory exists with algo_trading_manager.py
    pause
    exit /b 1
)

if not exist "config\credentials.py" (
    echo [ERROR] config\credentials.py not found
    echo [ERROR] Please ensure the config directory exists with credentials.py
    pause
    exit /b 1
)

echo [INFO] All required files found
echo.

REM Initialize MT5 terminals and start maintenance
echo [INFO] Initializing MT5 terminals...
echo [INFO] This may take a few moments...
echo.

REM Start the Python maintenance script
echo [INFO] Starting Algo Trading maintenance service...
echo [INFO] Monitoring all 5 MT5 terminals for Algo Trading status
echo.

python -c "import sys, time, logging; from datetime import datetime; logging.basicConfig(level=logging.INFO, format='[%%(asctime)s] %%(levelname)s - %%(message)s', datefmt='%%%%H:%%%%M:%%%%S'); exec('try:\\n    from utils.algo_trading_manager import keep_algo_trading_enabled, verify_all_terminals\\n    from config.credentials import MT5_TERMINALS\\n    print(\\\"[INFO] Starting continuous Algo Trading maintenance...\\\")\\n    print(\\\"[INFO] Monitoring terminals:\\\", list(MT5_TERMINALS.keys()))\\n    print(\\\"[INFO] Press Ctrl+C to stop\\\")\\n    print()\\n    keep_algo_trading_enabled()\\nexcept KeyboardInterrupt:\\n    print()\\n    print(\\\"[INFO] Maintenance service stopped by user\\\")\\n    print(\\\"[INFO] Algo Trading status may need manual monitoring\\\")\\nexcept Exception as e:\\n    print(f\\\"[ERROR] Error in maintenance service: {str(e)}\\\")\\n    import traceback\\n    traceback.print_exc()\\n    print()\\n    print(\\\"[ERROR] Maintenance service failed\\\")\\n    print(\\\"[ERROR] Please check the error above and restart\\\")\\nfinally:\\n    print()\\n    print(\\\"[INFO] Algo Trading maintenance service ended\\\")\\n    print(\\\"[WARNING] MT5 terminals may lose Algo Trading status without maintenance\\\")')"

echo.
echo [INFO] Algo Trading maintenance service has ended
echo [WARNING] MT5 terminals may lose Algo Trading status without this service
echo [INFO] You can restart this service by running maintain_algo_trading.bat again
echo.
pause
